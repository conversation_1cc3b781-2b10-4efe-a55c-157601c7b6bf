# AI运维代理系统架构设计

## 📋 系统概述

AI运维代理是一个智能化的服务监控和自动修复系统，具备持续监控、自动诊断和智能修复能力。系统集成了监控服务、AI诊断代理、MCP通信协议等核心组件，实现了完全自动化的运维流程。

## 🏗️ 系统架构图

```mermaid
graph TB
    %% 用户界面层
    subgraph "用户接口层"
        WebUI[Web管理界面]
        API[REST API服务]
        CLI[命令行接口]
    end

    %% 核心服务层
    subgraph "核心服务层"
        Monitor[监控服务<br/>MonitorService]
        Diagnostic[诊断代理<br/>DiagnosticAgent]
        AIModel[AI模型客户端<br/>LocalModelClient]
        Security[安全配置管理<br/>SecurityConfig]
    end

    %% 通信协议层
    subgraph "通信协议层"
        SSH_MCP[SSH MCP客户端<br/>远程命令执行]
        WeChat_MCP[微信MCP客户端<br/>告警通知]
        APIServer[API服务器<br/>FastAPI]
    end

    %% 外部系统层
    subgraph "外部系统"
        vLLM[vLLM模型服务<br/>AI推理引擎]
        RemoteServers[远程服务器<br/>目标监控主机]
        WeChatBot[企业微信机器人<br/>告警推送]
        ConfigFiles[配置文件<br/>服务映射配置]
    end

    %% 数据流向
    WebUI --> API
    CLI --> API
    API --> APIServer
    
    APIServer --> Monitor
    APIServer --> Diagnostic
    APIServer --> Security
    
    Monitor --> SSH_MCP
    Monitor --> WeChat_MCP
    Monitor -.->|检测到异常| Diagnostic
    
    Diagnostic --> AIModel
    Diagnostic --> SSH_MCP
    Diagnostic --> WeChat_MCP
    
    AIModel --> vLLM
    SSH_MCP --> RemoteServers
    WeChat_MCP --> WeChatBot
    
    Monitor --> ConfigFiles
    Security --> ConfigFiles

    %% 样式定义
    classDef primaryService fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef supportService fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef externalService fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef dataFlow fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px

    class Monitor,Diagnostic,AIModel primaryService
    class SSH_MCP,WeChat_MCP,APIServer supportService
    class vLLM,RemoteServers,WeChatBot,ConfigFiles externalService
    class API,WebUI,CLI dataFlow
```

## 🔄 监控与诊断流程

```mermaid
sequenceDiagram
    participant MS as 监控服务
    participant DA as 诊断代理
    participant AI as AI模型
    participant SSH as SSH MCP
    participant WX as 微信MCP
    participant RS as 远程服务器

    Note over MS,RS: 持续监控流程
    
    loop 每3分钟
        MS->>RS: 健康检查请求
        alt 服务正常
            RS-->>MS: 200 OK
            MS->>MS: 更新服务状态为正常
        else 服务异常
            RS-->>MS: 错误响应/超时
            MS->>MS: 记录错误，增加错误计数
            MS->>WX: 发送告警通知
            MS->>DA: 启动自动诊断
            
            Note over DA,RS: 自动诊断修复流程
            
            DA->>SSH: 获取服务状态和日志
            SSH->>RS: 执行状态检查命令
            RS-->>SSH: 返回状态信息
            SSH-->>DA: 返回诊断数据
            
            DA->>AI: 分析错误并生成修复方案
            AI-->>DA: 返回诊断结果和修复命令
            
            DA->>SSH: 执行修复命令
            SSH->>RS: 执行修复操作
            RS-->>SSH: 返回执行结果
            SSH-->>DA: 返回操作结果
            
            alt 修复成功
                DA->>WX: 发送修复成功通知
                DA->>MS: 通知修复完成
            else 修复失败
                DA->>DA: 尝试下一个修复方案
                alt 达到最大尝试次数
                    DA->>WX: 发送修复失败通知
                    DA->>MS: 通知修复失败
                end
            end
        end
    end
```

## 📊 组件详细设计

### 1. 监控服务 (MonitorService)

```mermaid
classDiagram
    class MonitorService {
        -services: Dict[str, Dict]
        -monitor_threads: Dict[str, Thread]
        -stop_flag: bool
        -diagnostic_agent: DiagnosticAgent
        -wechat_webhook_url: str
        
        +load_services_from_config() bool
        +add_service(name, url, ip) void
        +remove_service(name) void
        +start_monitoring() void
        +stop_monitoring() void
        +get_service_status(name) Dict
        +get_all_services_status() List[Dict]
        -_monitor_service(name) void
        -_handle_service_error(name, service) void
        -_send_wechat_alert(message) Dict
    }
    
    MonitorService --> DiagnosticAgent : 触发诊断
    MonitorService --> WechatMCP : 发送告警
    MonitorService --> ConfigFile : 加载配置
```

**核心功能**：
- ✅ **持续健康监控**：每3分钟检查一次服务状态
- ✅ **异常自动检测**：识别服务异常并记录错误次数
- ✅ **智能告警推送**：企业微信实时通知
- ✅ **诊断联动触发**：自动启动AI诊断流程

### 2. 诊断代理 (DiagnosticAgent)

```mermaid
classDiagram
    class DiagnosticAgent {
        -active_sessions: Dict[str, ContinuousDiagnosticSession]
        -diagnostic_history: List
        -global_attempt_strategies: List[str]
        
        +start_continuous_diagnosis() str
        +get_session_status(session_id) Dict
        +stop_session(session_id) bool
        +list_active_sessions() List[Dict]
        +get_diagnostic_history() List[Dict]
        -_continuous_diagnosis_loop(session_id) void
        -_perform_single_diagnosis_attempt() DiagnosticAttempt
        -_verify_service_recovery() bool
        -_generate_final_report() void
    }
    
    class ContinuousDiagnosticSession {
        +session_id: str
        +service_name: str
        +server_ip: str
        +initial_error: str
        +status: DiagnosticStatus
        +start_time: datetime
        +attempts: List[DiagnosticAttempt]
        +max_attempts: int
        +current_attempt: int
    }
    
    DiagnosticAgent --> ContinuousDiagnosticSession : 管理会话
    DiagnosticAgent --> AIModel : AI推理
    DiagnosticAgent --> SSH_MCP : 执行命令
```

**核心功能**：
- ✅ **持续诊断会话**：直到问题解决或方案用尽
- ✅ **AI智能分析**：基于错误信息生成修复方案
- ✅ **多策略尝试**：8种预定义修复策略
- ✅ **结果验证**：确认修复效果

### 3. MCP通信协议

```mermaid
graph LR
    subgraph "MCP架构"
        Client[MCP客户端]
        Protocol[MCP协议层]
        Server[MCP服务端]
    end
    
    subgraph "SSH MCP"
        SSHClient[SSH MCP客户端]
        SSHServer[SSH MCP服务]
        RemoteHost[远程主机]
    end
    
    subgraph "微信MCP"
        WXClient[微信MCP客户端]
        WXServer[微信MCP服务]
        WXBot[企业微信机器人]
    end
    
    Client --> Protocol
    Protocol --> Server
    
    SSHClient --> SSHServer
    SSHServer --> RemoteHost
    
    WXClient --> WXServer
    WXServer --> WXBot
```

**核心功能**：
- ✅ **SSH远程执行**：安全的远程命令执行
- ✅ **企业微信通知**：实时告警推送
- ✅ **协议标准化**：符合MCP协议规范

## 🚦 系统状态流转

```mermaid
stateDiagram-v2
    [*] --> 系统启动
    
    系统启动 --> 配置加载
    配置加载 --> 服务发现
    服务发现 --> 监控启动
    
    监控启动 --> 正常监控
    正常监控 --> 异常检测 : 服务异常
    异常检测 --> 告警发送
    告警发送 --> 诊断启动
    
    诊断启动 --> AI分析
    AI分析 --> 方案执行
    方案执行 --> 结果验证
    
    结果验证 --> 修复成功 : 验证通过
    结果验证 --> 重试诊断 : 验证失败
    
    修复成功 --> 正常监控
    重试诊断 --> AI分析 : 未达最大次数
    重试诊断 --> 修复失败 : 达到最大次数
    
    修复失败 --> 正常监控 : 人工介入
    
    正常监控 --> 系统关闭 : 停止信号
    系统关闭 --> [*]
```

## 🔒 安全设计

```mermaid
graph TB
    subgraph "安全层级"
        High[高安全模式<br/>预定义策略]
        Low[低安全模式<br/>完全自主]
    end
    
    subgraph "权限控制"
        Auth[身份认证]
        RBAC[基于角色的访问控制]
        Audit[操作审计]
    end
    
    subgraph "通信安全"
        SSH[SSH加密通道]
        API[API密钥认证]
        Token[JWT令牌验证]
    end
    
    High --> Auth
    Low --> Auth
    Auth --> RBAC
    RBAC --> Audit
    
    SSH --> Audit
    API --> Audit
    Token --> Audit
```

## 📈 性能指标

### 监控性能
- **监控间隔**：3分钟/次
- **响应时间**：< 30秒
- **并发监控**：支持100+服务
- **错误容忍**：3次连续失败触发诊断

### 诊断性能
- **诊断启动时间**：< 5秒
- **单次诊断耗时**：< 3分钟
- **最大尝试次数**：10次
- **并发诊断会话**：支持50+会话

### 通信性能
- **SSH连接超时**：30秒
- **MCP调用超时**：10秒
- **告警推送延迟**：< 5秒

## 🔄 扩展性设计

### 水平扩展
- **多实例部署**：支持负载均衡
- **分布式监控**：按区域分片
- **状态同步**：Redis/etcd集群

### 垂直扩展  
- **插件化架构**：支持自定义监控插件
- **策略扩展**：支持自定义修复策略
- **AI模型升级**：支持模型热更新

## 📋 部署架构

```mermaid
graph TB
    subgraph "生产环境"
        LB[负载均衡器]
        subgraph "AI运维代理集群"
            Agent1[代理实例1]
            Agent2[代理实例2]
            Agent3[代理实例3]
        end
        
        subgraph "AI服务层"
            vLLM1[vLLM实例1]
            vLLM2[vLLM实例2]
        end
        
        subgraph "存储层"
            Redis[Redis缓存]
            Logs[日志存储]
            Config[配置存储]
        end
    end
    
    subgraph "目标环境"
        Server1[服务器1]
        Server2[服务器2]
        ServerN[服务器N]
    end
    
    LB --> Agent1
    LB --> Agent2
    LB --> Agent3
    
    Agent1 --> vLLM1
    Agent2 --> vLLM2
    Agent3 --> vLLM1
    
    Agent1 --> Redis
    Agent2 --> Redis
    Agent3 --> Redis
    
    Agent1 --> Server1
    Agent2 --> Server2
    Agent3 --> ServerN
```

## 🎯 关键特性

### 1. 智能化
- ✅ **AI驱动诊断**：基于大语言模型的智能分析
- ✅ **自适应学习**：从历史案例中学习优化
- ✅ **上下文感知**：结合服务特征和环境信息

### 2. 自动化
- ✅ **零人工干预**：从监控到修复的全自动流程
- ✅ **持续运行**：7×24小时不间断监控
- ✅ **智能重试**：失败后自动尝试不同策略

### 3. 可观测性
- ✅ **实时监控**：服务状态实时展示
- ✅ **详细日志**：完整的操作审计轨迹
- ✅ **性能指标**：监控和诊断性能统计

### 4. 可靠性
- ✅ **故障隔离**：单个服务故障不影响整体
- ✅ **优雅降级**：外部依赖失败时的降级策略
- ✅ **数据持久化**：重要状态和历史数据保存

---

*文档版本：v2.0*  
*最后更新：2025-06-18*  
*维护者：AI运维代理开发团队* 