# 企业微信MCP服务器配置指南

## 🎉 恭喜！环境变量问题已解决

您的MCP服务器现在可以正确读取.env文件中的配置了！

## 📋 下一步操作

### 1. 获取真实的企业微信Webhook URL

当前使用的是示例URL，需要替换为真实的企业微信机器人webhook：

1. **打开企业微信群聊**
2. **点击群设置 → 群机器人 → 添加机器人**
3. **创建机器人并复制Webhook URL**
4. **修改.env文件中的URL**

### 2. 更新.env文件

将您的真实webhook URL替换示例URL：

```bash
# 将下面的示例URL替换为您的真实URL
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_REAL_KEY
WECHAT_ENABLED=true
```

### 3. 测试连接

```bash
uv run python test_mcp.py
```

如果配置正确，您应该会看到：
- ✅ 文本消息: 成功
- ✅ Markdown消息: 成功

### 4. 配置Claude Desktop

在 `%APPDATA%\Claude\claude_desktop_config.json` 中添加：

```json
{
  "mcpServers": {
    "wechat-mcp-server": {
      "command": "uv",
      "args": ["run", "python", "main.py"],
      "cwd": "D:/path/to/your/wechat_mcp",
      "env": {
        "WECHAT_WEBHOOK_URL": "您的真实webhook URL",
        "WECHAT_ENABLED": "true"
      }
    }
  }
}
```

### 5. 重启Claude Desktop

重启Claude Desktop以加载MCP服务器。

## 🧪 使用示例

配置完成后，您可以对Claude说：

```
"请发送一个系统警报，nginx服务在192.168.1.100出现连接超时，严重程度是高"
```

Claude会自动调用MCP工具发送格式化的警报到企业微信群。

## 🔧 故障排除

如果遇到问题：

1. **检查webhook URL**: 确保URL完整且有效
2. **检查网络连接**: 确保能访问企业微信API
3. **查看日志**: 运行测试脚本查看详细错误信息
4. **验证权限**: 确保机器人在对应的企业微信群中

## 📞 技术支持

如果遇到技术问题，请检查：
- .env文件编码是否为UTF-8
- webhook URL是否包含正确的key参数
- 企业微信群机器人是否正常工作 