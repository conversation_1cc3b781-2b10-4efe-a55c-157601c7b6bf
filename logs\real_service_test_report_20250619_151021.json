{"test_type": "real_services", "timestamp": "2025-06-19T15:10:21.916389", "total_tests": 12, "passed_tests": 6, "failed_tests": 6, "success_rate": 50.0, "services_tested": ["dify", "ragflow", "ocr_1111_test"], "results": [{"test_name": "API服务器连通性", "success": true, "message": "API服务器响应正常", "timestamp": "2025-06-19T15:09:58.630215"}, {"test_name": "SSH MCP连通性", "success": true, "message": "成功获取 2 个服务", "timestamp": "2025-06-19T15:10:00.095650"}, {"test_name": "微信MCP连通性", "success": true, "message": "测试消息发送成功", "timestamp": "2025-06-19T15:10:01.478386"}, {"test_name": "服务状态-dify", "success": true, "message": "状态: unknown", "timestamp": "2025-06-19T15:10:02.935403"}, {"test_name": "服务日志-dify", "success": false, "message": "'dict' object has no attribute 'splitlines'", "timestamp": "2025-06-19T15:10:04.286817"}, {"test_name": "服务状态-ragflow", "success": true, "message": "状态: unknown", "timestamp": "2025-06-19T15:10:05.673817"}, {"test_name": "服务日志-ragflow", "success": false, "message": "'dict' object has no attribute 'splitlines'", "timestamp": "2025-06-19T15:10:07.016704"}, {"test_name": "服务状态-ocr_1111_test", "success": true, "message": "状态: unknown", "timestamp": "2025-06-19T15:10:08.333282"}, {"test_name": "服务日志-ocr_1111_test", "success": false, "message": "'dict' object has no attribute 'splitlines'", "timestamp": "2025-06-19T15:10:09.756566"}, {"test_name": "AI诊断-dify", "success": false, "message": "未返回会话ID", "timestamp": "2025-06-19T15:10:10.799795"}, {"test_name": "AI诊断-ragflow", "success": false, "message": "未返回会话ID", "timestamp": "2025-06-19T15:10:14.850402"}, {"test_name": "AI诊断-ocr_1111_test", "success": false, "message": "未返回会话ID", "timestamp": "2025-06-19T15:10:18.899038"}]}