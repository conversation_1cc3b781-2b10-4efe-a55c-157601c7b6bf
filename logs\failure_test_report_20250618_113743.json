{"test_type": "failure_scenarios", "summary": {"total_tests": 5, "passed_tests": 2, "failed_tests": 3, "success_rate": 40.0}, "results": [{"test_name": "API连通性", "success": true, "message": "API服务响应正常", "timestamp": "2025-06-18T11:37:29.512161"}, {"test_name": "监控状态获取", "success": true, "message": "监控中的服务数: 1", "timestamp": "2025-06-18T11:37:30.517545"}, {"test_name": "微信通知测试", "success": false, "message": "通知发送失败", "timestamp": "2025-06-18T11:37:31.531355"}, {"test_name": "手动诊断触发", "success": false, "message": "状态码: 500", "timestamp": "2025-06-18T11:37:32.554433"}, {"test_name": "手动诊断触发", "success": false, "message": "状态码: 500", "timestamp": "2025-06-18T11:37:43.587777"}]}