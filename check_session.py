#!/usr/bin/env python3
import requests
import json
import sys

def check_session(session_id):
    """检查诊断会话状态"""
    try:
        resp = requests.get(f'http://localhost:8000/session/{session_id}', timeout=10)
        if resp.status_code == 200:
            data = resp.json().get('data', {})
            print("=" * 60)
            print(f"📊 诊断会话详情: {session_id}")
            print("=" * 60)
            
            print(f"状态: {data.get('status', 'unknown')}")
            print(f"当前尝试: {data.get('current_attempt', 0)}")
            print(f"是否活跃: {data.get('is_active', False)}")
            print(f"服务名: {data.get('service_name', 'N/A')}")
            print(f"服务器IP: {data.get('server_ip', 'N/A')}")
            print(f"初始错误: {data.get('initial_error', 'N/A')}")
            print(f"最新诊断: {data.get('latest_diagnosis', 'N/A')}")
            
            print("\\n🔍 详细信息:")
            print(json.dumps(data, ensure_ascii=False, indent=2))
            
        else:
            print(f"❌ 获取会话失败: {resp.status_code}")
            
    except Exception as e:
        print(f"❌ 检查会话异常: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        session_id = sys.argv[1]
    else:
        session_id = "nginx_web_server_10.20.30.40_20250619_145407"
    
    check_session(session_id) 