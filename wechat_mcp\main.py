#!/usr/bin/env python3
"""
企业微信机器人MCP服务器
提供企业微信通知功能的MCP工具
"""

import asyncio
import json
import os
from typing import Any, Dict, List, Optional

import httpx
from mcp.server import Server
from mcp.server import NotificationOptions
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    Tool,
    TextContent,
)
from loguru import logger
from pydantic import BaseModel, Field

# 辅助函数：生成符合 MCP TextContent 的文本对象
def _tc(msg: str) -> TextContent:  # noqa: D401
    """简易包装，将字符串转换为 MCP TextContent。"""
    return TextContent(type="text", text=str(msg))


class WechatConfig(BaseModel):
    """企业微信配置模型"""
    webhook_url: str = Field(..., description="企业微信机器人webhook URL")
    enabled: bool = Field(default=True, description="是否启用企业微信通知")


class WechatBotMCP:
    """企业微信机器人MCP客户端"""
    
    def __init__(self, config: WechatConfig):
        self.config = config
        
    async def send_message(self, message: Dict[str, Any]) -> bool:
        """发送消息到企业微信机器人webhook"""
        if not self.config.enabled:
            logger.info("企业微信机器人推送已禁用，跳过消息发送")
            return False
            
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.config.webhook_url,
                    json=message,
                    headers={"Content-Type": "application/json"},
                    timeout=10.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        logger.info("企业微信通知发送成功")
                        return True
                    else:
                        logger.error(f"企业微信通知发送失败: {result.get('errmsg')}")
                else:
                    logger.error(f"企业微信通知发送失败，状态码: {response.status_code}")
                
                return False
        except Exception as e:
            logger.error(f"发送企业微信消息时发生错误: {e}")
            return False
    
    async def send_text_message(self, content: str, mentioned_list: Optional[List[str]] = None) -> bool:
        """发送文本消息"""
        message = {
            "msgtype": "text",
            "text": {
                "content": content
            }
        }
        
        if mentioned_list:
            message["text"]["mentioned_list"] = mentioned_list
            
        return await self.send_message(message)
    
    async def send_markdown_message(self, content: str) -> bool:
        """发送markdown消息"""
        message = {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }
        
        return await self.send_message(message)


# 全局变量存储配置
wechat_bot: Optional[WechatBotMCP] = None

# 创建MCP服务器
server = Server("wechat-mcp-server")


@server.list_tools()
async def handle_list_tools() -> List[Tool]:
    """列出可用的工具"""
    return [
        Tool(
            name="send_text_message",
            description="发送文本消息到企业微信群",
            inputSchema={
                "type": "object",
                "properties": {
                    "content": {
                        "type": "string",
                        "description": "要发送的文本消息内容，最长不超过2048个字节"
                    },
                    "mentioned_list": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "提醒人员列表，可选。使用@all表示提醒所有人",
                        "default": []
                    }
                },
                "required": ["content"]
            }
        ),
        Tool(
            name="send_markdown_message", 
            description="发送Markdown格式消息到企业微信群",
            inputSchema={
                "type": "object",
                "properties": {
                    "content": {
                        "type": "string",
                        "description": "要发送的Markdown格式消息内容"
                    }
                },
                "required": ["content"]
            }
        ),
        Tool(
            name="send_system_alert",
            description="发送系统警报通知，大模型会根据参数生成合适的消息格式",
            inputSchema={
                "type": "object", 
                "properties": {
                    "alert_type": {
                        "type": "string",
                        "enum": ["error", "warning", "info", "success"],
                        "description": "警报类型"
                    },
                    "service_name": {
                        "type": "string",
                        "description": "服务名称"
                    },
                    "server_info": {
                        "type": "string", 
                        "description": "服务器信息（IP或主机名）"
                    },
                    "description": {
                        "type": "string",
                        "description": "问题描述或详细信息"
                    },
                    "severity": {
                        "type": "string",
                        "enum": ["low", "medium", "high", "critical"],
                        "description": "严重程度",
                        "default": "medium"
                    }
                },
                "required": ["alert_type", "service_name", "server_info", "description"]
            }
        ),
        Tool(
            name="send_diagnosis_update",
            description="发送诊断进度更新通知",
            inputSchema={
                "type": "object",
                "properties": {
                    "session_id": {
                        "type": "string", 
                        "description": "诊断会话ID"
                    },
                    "service_name": {
                        "type": "string",
                        "description": "服务名称"
                    },
                    "server_info": {
                        "type": "string",
                        "description": "服务器信息"
                    },
                    "status": {
                        "type": "string",
                        "enum": ["started", "in_progress", "completed", "failed"],
                        "description": "诊断状态"
                    },
                    "message": {
                        "type": "string",
                        "description": "状态描述或结果信息"
                    },
                    "attempts": {
                        "type": "integer",
                        "description": "尝试次数",
                        "default": 1
                    }
                },
                "required": ["session_id", "service_name", "server_info", "status", "message"]
            }
        )
    ]


@server.call_tool()
async def handle_call_tool(name: str, arguments: dict) -> CallToolResult:
    """处理工具调用"""
    if wechat_bot is None:
        return CallToolResult(
            content=[_tc("企业微信机器人未初始化，请检查配置")],
            isError=True
        )
    
    try:
        if name == "send_text_message":
            content = arguments["content"]
            mentioned_list = arguments.get("mentioned_list", [])
            
            success = await wechat_bot.send_text_message(content, mentioned_list or None)
            
            if success:
                return CallToolResult(
                    content=[_tc("文本消息发送成功")]
                )
            else:
                return CallToolResult(
                    content=[_tc("文本消息发送失败")],
                    isError=True
                )
        
        elif name == "send_markdown_message":
            content = arguments["content"]
            
            success = await wechat_bot.send_markdown_message(content)
            
            if success:
                return CallToolResult(
                    content=[_tc("Markdown消息发送成功")]
                )
            else:
                return CallToolResult(
                    content=[_tc("Markdown消息发送失败")],
                    isError=True
                )
        
        elif name == "send_system_alert":
            alert_type = arguments["alert_type"]
            service_name = arguments["service_name"]
            server_info = arguments["server_info"]
            description = arguments["description"]
            severity = arguments.get("severity", "medium")
            
            # 根据警报类型和严重程度生成消息
            emoji_map = {
                "error": "❌",
                "warning": "⚠️", 
                "info": "ℹ️",
                "success": "✅"
            }
            
            color_map = {
                "error": "warning",
                "warning": "warning", 
                "info": "info",
                "success": "info"
            }
            
            severity_map = {
                "low": "🔵",
                "medium": "🟡",
                "high": "🟠", 
                "critical": "🔴"
            }
            
            emoji = emoji_map.get(alert_type, "ℹ️")
            color = color_map.get(alert_type, "info")
            severity_emoji = severity_map.get(severity, "🟡")
            
            content = f"""### {emoji} 系统警报通知
- **服务**: <font color=\"{color}\">{service_name}</font>
- **服务器**: {server_info}
- **严重程度**: {severity_emoji} {severity.upper()}
- **类型**: {alert_type.upper()}
- **描述**: {description}

请及时关注并处理相关问题。
            """
            
            success = await wechat_bot.send_markdown_message(content)
            
            if success:
                return CallToolResult(
                    content=[_tc("系统警报通知发送成功")]
                )
            else:
                return CallToolResult(
                    content=[_tc("系统警报通知发送失败")],
                    isError=True
                )
        
        elif name == "send_diagnosis_update":
            session_id = arguments["session_id"]
            service_name = arguments["service_name"]
            server_info = arguments["server_info"]
            status = arguments["status"]
            message = arguments["message"]
            attempts = arguments.get("attempts", 1)
            
            # 根据状态生成相应的消息
            status_map = {
                "started": ("🚀", "info", "诊断已启动"),
                "in_progress": ("⏳", "info", "诊断进行中"),
                "completed": ("✅", "info", "诊断已完成"),
                "failed": ("❌", "warning", "诊断失败")
            }
            
            emoji, color, status_text = status_map.get(status, ("ℹ️", "info", "状态更新"))
            
            content = f"""### {emoji} AI诊断进度更新
- **服务**: <font color=\"{color}\">{service_name}</font>
- **服务器**: {server_info}
- **会话ID**: {session_id}
- **状态**: <font color=\"{color}\">{status_text}</font>
- **尝试次数**: {attempts}
- **详情**: {message}

诊断进度实时更新中...
            """
            
            success = await wechat_bot.send_markdown_message(content)
            
            if success:
                return CallToolResult(
                    content=[_tc("诊断更新通知发送成功")]
                )
            else:
                return CallToolResult(
                    content=[_tc("诊断更新通知发送失败")],
                    isError=True
                )
        
        else:
            return CallToolResult(
                content=[_tc(f"未知的工具: {name}")],
                isError=True
            )
    
    except Exception as e:
        logger.error(f"处理工具调用时发生错误: {e}")
        return CallToolResult(
            content=[_tc(f"工具调用失败: {str(e)}")],
            isError=True
        )


async def main():
    """主函数"""
    global wechat_bot
    
    # 从环境变量读取配置
    webhook_url = os.getenv("WECHAT_WEBHOOK_URL")
    enabled = os.getenv("WECHAT_ENABLED", "true").lower() == "true"
    
    if not webhook_url:
        logger.error("未找到企业微信Webhook URL，请设置环境变量 WECHAT_WEBHOOK_URL")
        return
    
    # 初始化企业微信客户端
    config = WechatConfig(webhook_url=webhook_url, enabled=enabled)
    wechat_bot = WechatBotMCP(config)
    
    logger.info(f"企业微信MCP服务器启动，状态: {'启用' if enabled else '禁用'}")
    
    # 启动stdio服务器
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="wechat-mcp-server",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(
                        tools_changed=True,
                        prompts_changed=False,
                        resources_changed=False
                    ),
                    experimental_capabilities={}
                )
            )
        )


if __name__ == "__main__":
    asyncio.run(main())
