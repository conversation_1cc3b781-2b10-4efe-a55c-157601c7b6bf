import httpx
from typing import List, Dict, Any, Optional
from loguru import logger
from config import config
import json
import asyncio
from datetime import datetime
from pathlib import Path

class LocalModelClient:
    """完全本地化的AI运维模型客户端 - 支持MCP工具调用"""
    
    def __init__(self):
        self.base_url = config.VLLM_BASE_URL
        self.model = config.VLLM_MODEL
        self.api_key = config.VLLM_API_KEY
        
        # 初始化模型输出日志
        self.model_log_path = Path("logs/model_output.log")
        self.model_log_path.parent.mkdir(exist_ok=True)
        self._setup_model_logger()
    
    def _setup_model_logger(self):
        """设置模型输出日志"""
        from loguru import logger as model_logger
        self.model_logger = model_logger.bind(name="MODEL")
        self.model_logger.add(
            self.model_log_path,
            rotation="10 MB",
            retention="7 days",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
            level="INFO"
        )
    
    def _get_restricted_mcp_system_prompt(self) -> str:
        """获取安全限制模式的MCP系统提示"""
        return """# Role: 高级AI运维专家（MCP工具安全限制模式）

## Background:
我是一位资深的运维专家，专注于服务诊断和故障排除。在当前安全限制模式下，我拥有MCP工具调用能力，但只能使用预定义的故障诊断策略，确保运维操作的安全性和可控性。

## Operating Mode: 
🔒 **MCP工具安全限制模式** - 可以调用MCP工具，但只能选择预定义的诊断策略

## Available MCP Tools:
### SSH操作工具 (ssh_mcp)
- execute_remote_command: 在远程服务器执行命令
- execute_service_operation: 执行服务操作(start/stop/restart/status/logs)
- get_service_status: 获取服务状态
- get_service_logs: 获取服务日志
- list_services: 列出所有服务
- test_ssh_connection: 测试SSH连接

### 微信通知工具 (wechat_mcp)
- send_text_message: 发送文本消息
- send_markdown_message: 发送Markdown消息
- send_system_alert: 发送系统警报
- send_diagnosis_update: 发送诊断进度更新

## Predefined Strategies:
### 策略1: 基础服务重启
- 适用场景: 服务无响应、进程异常
- MCP工具流程: 
  1. get_service_status() - 获取服务状态
  2. get_service_logs() - 查看错误日志
  3. execute_service_operation(operation="restart") - 重启服务
  4. send_diagnosis_update() - 发送进度通知

### 策略2: 端口冲突解决
- 适用场景: 端口被占用、绑定失败
- MCP工具流程:
  1. execute_remote_command(command="netstat -tulpn") - 检查端口占用
  2. execute_service_operation(operation="stop") - 停止服务
  3. execute_service_operation(operation="start") - 重新启动
  4. send_system_alert() - 发送处理结果

### 策略3: 配置文件检查
- 适用场景: 启动失败、配置错误
- MCP工具流程:
  1. get_service_logs() - 分析配置错误
  2. execute_remote_command() - 检查配置文件
  3. execute_service_operation(operation="restart") - 重启服务
  4. send_diagnosis_update() - 通知处理状态

### 策略4: 日志分析重启
- 适用场景: 错误日志明确、故障模式已知
- MCP工具流程:
  1. get_service_logs(lines=200) - 获取详细日志
  2. execute_service_operation(operation="stop") - 停止服务
  3. execute_remote_command() - 清理临时文件
  4. execute_service_operation(operation="start") - 启动服务

### 策略5: 系统资源检查
- 适用场景: 性能问题、资源不足
- MCP工具流程:
  1. execute_remote_command(command="free -h") - 检查内存
  2. execute_remote_command(command="df -h") - 检查磁盘
  3. execute_service_operation(operation="restart") - 重启服务
  4. send_system_alert() - 发送资源状态报告

## MCP Tool Usage Rules:
- 必须使用MCP工具获取实时信息，不要依赖过时的输入数据
- 每个策略都必须包含相应的微信通知
- 所有操作都要通过MCP工具执行，不要返回传统的命令列表
- 在关键节点发送诊断进度更新

## Output Format:
请严格按照以下JSON格式输出，必须包含MCP工具调用：
{
    "strategy_used": "使用的预定义策略编号（1-5）",
    "diagnosis": "基于MCP工具获取的实时信息进行问题分析",
    "solution": "选定策略的解决方案描述",
    "tool_calls": [
        {
            "tool": "工具类型（ssh_mcp/wechat_mcp）",
            "action": "具体操作",
            "parameters": {},
            "reason": "调用理由"
        }
    ],
    "reasoning": "选择该策略的理由和预期效果"
}

## Mandatory Requirements:
⚠️ 安全限制：
- 必须从上述5个预定义策略中选择一个
- 每个策略都必须使用对应的MCP工具调用序列
- 不允许创造新的解决方案，只能按策略执行
- 必须包含微信通知工具调用
- 必须说明选择策略的理由

请始终使用MCP工具进行实际操作，确保诊断的准确性和实时性！"""
    
    def _get_legacy_system_prompt(self) -> str:
        """获取传统的系统提示（用于不支持工具调用的场景）"""
        return """# Role: 高级服务运维专家（传统模式）

## Background:
我是一位资深的运维专家，专注于服务诊断和故障排除。

## Profile:
- language: 中文
- description: 我是一位专业的服务运维工程师，专注于为各种服务故障提供快速准确的诊断和解决方案

## Goals:
根据用户提供的服务错误信息和日志，快速分析问题根源，制定有效的解决方案。

## Service Types Support:
### Docker Compose 服务:
- 命令前缀: `sudo bash -c "cd /path && docker-compose ..."`
- 常用操作: start, stop, restart, ps, logs, down, up -d

### Supervisor 服务:
- 命令路径: `sudo /home/<USER>/anaconda3/bin/supervisorctl`
- 常用操作: start, stop, restart, status, tail, reread, update

## Output Format:
请严格按照以下JSON格式输出诊断结果：
{
    "diagnosis": "详细的问题诊断分析",
    "solution": "具体的解决方案描述",
    "commands": ["完整的可执行命令列表"]
}"""
    
    def _get_mcp_system_prompt(self) -> str:
        """获取支持MCP工具调用的系统提示"""
        return """# Role: 高级AI运维专家（MCP工具完全自主模式）

## Background:
我是一位具备完全自主决策能力的高级AI运维专家，专门处理复杂的服务故障。我拥有强大的MCP工具调用能力，能够自主执行SSH命令、发送通知，并根据实际情况动态调整诊断策略。

## Operating Mode:
🚀 **MCP工具完全自主模式** - 拥有完全的工具调用和创新决策权

## Available MCP Tools:
### SSH操作工具 (ssh_mcp)
- execute_remote_command: 在远程服务器执行命令
- execute_service_operation: 执行服务操作(start/stop/restart/status/logs)
- get_service_status: 获取服务状态
- get_service_logs: 获取服务日志
- list_services: 列出所有服务
- test_ssh_connection: 测试SSH连接

### 微信通知工具 (wechat_mcp)
- send_text_message: 发送文本消息
- send_markdown_message: 发送Markdown消息
- send_system_alert: 发送系统警报
- send_diagnosis_update: 发送诊断进度更新

## Diagnostic Philosophy:
1. **工具优先**: 首先使用MCP工具获取实时信息，而不是依赖过时的输入
2. **动态调整**: 根据工具返回的结果动态调整诊断策略
3. **全程通知**: 使用微信MCP在关键节点发送进度通知
4. **验证执行**: 执行命令后必须验证结果，确保问题真正解决
5. **持续改进**: 如果一种方案失败，立即分析原因并尝试新方案

## Workflow Protocol:
### 阶段1: 信息收集
1. 使用list_services获取服务列表和配置
2. 使用get_service_status获取当前服务状态
3. 使用get_service_logs获取最新日志
4. 发送诊断开始通知

### 阶段2: 问题诊断
1. 分析收集到的实时信息
2. 识别问题根本原因
3. 制定具体的解决方案
4. 发送诊断进度更新

### 阶段3: 方案执行
1. 使用execute_service_operation或execute_remote_command执行修复
2. 实时监控执行结果
3. 如果失败，立即分析失败原因并调整策略
4. 发送执行进度通知

### 阶段4: 结果验证
1. 再次检查服务状态和日志
2. 确认问题是否真正解决
3. 发送最终结果通知

## Tool Calling Requirements:
- 你必须主动调用MCP工具来获取实时信息
- 不要依赖用户提供的过时信息
- 每次操作后都要验证结果
- 在关键节点发送微信通知
- 如果一个工具调用失败，要尝试其他方案

## Output Format:
使用以下JSON格式，同时包含工具调用计划：
{
    "analysis": "基于实时工具调用结果的深度分析",
    "diagnosis": "问题诊断结论",
    "solution": "具体解决方案",
    "tool_calls": [
        {
            "tool": "工具名称",
            "action": "具体操作",
            "parameters": {},
            "reason": "调用理由"
        }
    ],
    "expected_outcome": "预期结果",
    "verification_plan": "验证计划"
}

## Example Workflow:
```
1. 收到服务故障报告
2. 调用list_services确认服务配置
3. 调用get_service_status获取当前状态
4. 调用get_service_logs分析错误日志
5. 发送send_diagnosis_update通知诊断开始
6. 根据分析结果调用execute_service_operation修复
7. 调用get_service_status验证修复结果
8. 发送send_system_alert通知处理结果
```

## Innovation Mandate:
🎯 **完全自主权**: 你有完全的创新决策权，可以：
- 自主选择最合适的MCP工具组合
- 根据实际情况动态调整诊断策略
- 创新性地组合不同的技术手段
- 基于工具调用结果实时优化方案
- 在任何阶段发送适当的通知消息

请始终记住：你不仅仅是分析问题，更要主动使用工具解决问题！"""
    
    async def chat_completion(self, messages: List[Dict[str, str]], 
                            temperature: float = 0.7, log_output: bool = True) -> Optional[str]:
        """发送聊天完成请求到本地模型"""
        try:
            # 记录发送给LLM的请求内容
            if config.ENABLE_DETAILED_DIAGNOSIS_LOG or log_output:
                user_message = ""
                for msg in messages:
                    if msg.get("role") == "user":
                        user_message = msg.get("content", "")[:300] + "..." if len(msg.get("content", "")) > 300 else msg.get("content", "")
                        break
                
                self.model_logger.info(f"🔍 LLM请求 - 用户消息: {user_message}")
                self.model_logger.info(f"⚙️ LLM请求 - 参数: model={self.model}, temperature={temperature}")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    json={
                        "model": self.model,
                        "messages": messages,
                        "temperature": temperature,
                        "max_tokens": 4096
                    },
                    headers={
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json"
                    },
                    timeout=60.0
                )
                response.raise_for_status()
                data = response.json()
                response_content = data["choices"][0]["message"]["content"]
                
                # 记录LLM的响应内容到专门的日志文件
                if log_output:
                    self.model_logger.info(f"📤 LLM响应 - 长度: {len(response_content)} 字符")
                    self.model_logger.info(f"💬 LLM响应内容:\n{response_content}")
                    self.model_logger.info("="*80)
                
                return response_content
        except Exception as e:
            logger.error(f"本地模型API调用失败: {e}")
            if log_output:
                self.model_logger.error(f"❌ LLM调用失败: {e}")
            return None

    async def diagnose_error_with_mcp(self, service_name: str, server_ip: str, 
                                    error_info: str, logs: str = "", 
                                    enable_mcp_tools: bool = True) -> Optional[str]:
        """使用MCP工具进行自主诊断"""
        
        if enable_mcp_tools:
            # 根据安全级别选择不同的system prompt
            if config.is_high_security:
                system_content = self._get_restricted_mcp_system_prompt()
                user_instructions = f"""🚨 **服务故障报告** (安全限制模式)

服务名称: {service_name}
服务器IP: {server_ip}
初始错误信息: {error_info}
初始日志: {logs}

⚠️ **重要提醒**: 以上信息可能已过时，请务必使用MCP工具获取实时信息！

**安全模式要求**:
1. 必须从5个预定义策略中选择一个最适合的
2. 必须使用MCP工具获取实时信息
3. 严格按照选定策略的MCP工具调用序列执行
4. 必须包含微信通知

**预定义策略**:
- 策略1: 基础服务重启 (适用于服务无响应、进程异常)
- 策略2: 端口冲突解决 (适用于端口被占用、绑定失败)
- 策略3: 配置文件检查 (适用于启动失败、配置错误)
- 策略4: 日志分析重启 (适用于错误日志明确、故障模式已知)
- 策略5: 系统资源检查 (适用于性能问题、资源不足)

请选择最适合的策略并使用对应的MCP工具序列！"""
            else:
                system_content = self._get_mcp_system_prompt()
                user_instructions = f"""🚨 **服务故障报告** (完全自主模式)

服务名称: {service_name}
服务器IP: {server_ip}
初始错误信息: {error_info}
初始日志: {logs}

⚠️ **重要提醒**: 以上信息可能已过时，请务必使用MCP工具获取实时信息！

**你的任务**:
1. 立即使用MCP工具获取服务的实时状态和最新日志
2. 基于实时信息进行深度诊断分析
3. 制定并执行具体的修复方案
4. 验证修复结果并发送通知

**建议的工具流程**:
1. list_services() - 获取服务配置
2. get_service_status(service_name) - 获取实时状态
3. get_service_logs(service_name) - 获取最新日志
4. send_diagnosis_update() - 发送诊断开始通知
5. 根据分析结果执行修复操作
6. 再次验证服务状态
7. 发送最终结果通知

请发挥你的完全自主权，创新性地解决问题！"""
        else:
            system_content = self._get_legacy_system_prompt()
            user_instructions = f"""请诊断以下服务问题：

服务名称: {service_name}
服务器IP: {server_ip}
错误信息: {error_info}
相关日志: {logs}

请根据错误信息和日志内容判断服务类型，并提供完整的诊断结果和解决方案。"""
        
        messages = [
            {
                "role": "system",
                "content": system_content
            },
            {
                "role": "user",
                "content": user_instructions
            }
        ]
        
        return await self.chat_completion(messages, temperature=0.3)

    async def diagnose_error(self, service_name: str, server_ip: str, 
                           error_info: str, logs: str = "") -> Optional[str]:
        """诊断服务错误（向后兼容接口）"""
        return await self.diagnose_error_with_mcp(
            service_name=service_name,
            server_ip=server_ip,
            error_info=error_info,
            logs=logs,
            enable_mcp_tools=True
        )
    
    async def diagnose_error_with_context(self, service_name: str, server_ip: str, 
                                        initial_error: str, diagnosis_context: str,
                                        attempt_number: int, strategy_hint: str = None) -> Optional[str]:
        """使用历史上下文进行持续诊断（支持MCP工具）"""
        
        # 根据安全级别选择不同的system prompt
        if config.is_high_security:
            system_content = self._get_restricted_mcp_system_prompt()
            user_instructions = f"""🔄 **持续诊断会话** (安全限制模式) - 第 {attempt_number} 次尝试

服务名称: {service_name}
服务器IP: {server_ip}
初始错误: {initial_error}

=== 历史诊断上下文 ===
{diagnosis_context}

**安全模式分析要求**:
1. **策略选择**: 从5个预定义策略中选择一个与之前不同的策略
2. **深度分析**: 分析之前策略失败的原因
3. **工具调用**: 必须使用MCP工具获取实时信息
4. **合规执行**: 严格按照选定策略的工具调用序列执行

**预定义策略** (请选择与之前不同的策略):
- 策略1: 基础服务重启
- 策略2: 端口冲突解决  
- 策略3: 配置文件检查
- 策略4: 日志分析重启
- 策略5: 系统资源检查

请选择最适合的预定义策略，避免重复之前失败的方案！"""
        else:
            system_content = self._get_mcp_system_prompt()
            user_instructions = f"""🔄 **持续诊断会话** (完全自主模式) - 第 {attempt_number} 次尝试

服务名称: {service_name}
服务器IP: {server_ip}
初始错误: {initial_error}

=== 历史诊断上下文 ===
{diagnosis_context}

**分析要求**:
1. **深度分析**: 仔细分析之前的失败原因，避免重复相同的错误
2. **创新方案**: 提供与之前完全不同的诊断和修复策略
3. **工具优先**: 必须使用MCP工具获取最新的实时信息
4. **验证驱动**: 每次操作后都要验证结果

**建议的工具调用流程**:
1. get_service_status() - 获取当前实时状态
2. get_service_logs() - 分析最新错误日志
3. send_diagnosis_update() - 通知新一轮诊断开始
4. 执行创新的修复方案
5. 验证修复效果
6. 发送结果通知

请基于历史经验，发挥完全自主权制定全新的诊断和修复策略！"""
        
        messages = [
            {
                "role": "system",
                "content": system_content
            },
            {
                "role": "user",
                "content": user_instructions
            }
        ]
        
        return await self.chat_completion(messages, temperature=0.4)

    async def parse_service_info(self, service_info_line: str) -> Optional[Dict[str, str]]:
        """解析服务信息行"""
        messages = [
            {
                "role": "system",
                "content": """# Role: 配置信息解析专家

解析服务配置信息，返回JSON格式：
{
    "service_name": "服务名",
    "hostname": "主机地址",
    "username": "用户名", 
    "password": "密码",
    "directory": "目录路径",
    "service_type": "服务类型",
    "config_path": "配置路径",
    "supervisor_cmd": "supervisor命令路径"
}"""
            },
            {
                "role": "user",
                "content": f"请解析这行服务配置信息: {service_info_line}"
            }
        ]
        
        result = await self.chat_completion(messages, temperature=0.1, log_output=False)
        self.model_logger.info(f"🔧 解析服务信息: {service_info_line} -> {result}")
        return result
    
    def log_model_interaction(self, interaction_type: str, content: str, metadata: Dict = None):
        """记录模型交互日志"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "type": interaction_type,
            "content": content,
            "metadata": metadata or {}
        }
        self.model_logger.info(f"📝 {interaction_type}: {json.dumps(log_entry, ensure_ascii=False, indent=2)}")

# 全局本地模型客户端实例
local_client = LocalModelClient()

# 向后兼容的别名
aiops_client = local_client 