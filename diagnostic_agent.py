import json
import asyncio
from typing import Dict, List, Optional, Any
from loguru import logger
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from model_client import aiops_client
from config import config
# 更新导入：使用SSH MCP客户端替代原SSH客户端
from ssh_mcp_client import ssh_mcp_client
# 暂时注释掉微信MCP的导入
# from wechat_mcp_client import wechat_mcp_client

# 创建一个模拟的微信MCP客户端
class MockWeChatMCPClient:
    def __init__(self):
        self.enabled = False
    
    async def send_diagnosis_update(self, *args, **kwargs):
        return True
    
    async def send_system_alert(self, *args, **kwargs):
        return True
    
    async def send_text_message(self, *args, **kwargs):
        return True
    
    async def send_markdown_message(self, *args, **kwargs):
        return True

# 使用模拟客户端
wechat_mcp_client = MockWeChatMCPClient()

class DiagnosticStatus(Enum):
    """诊断状态枚举"""
    PENDING = "pending"           # 等待中
    DIAGNOSING = "diagnosing"     # 诊断中
    EXECUTING = "executing"       # 执行中
    RESOLVED = "resolved"         # 已解决
    FAILED = "failed"             # 失败
    EXHAUSTED = "exhausted"       # 方案已用尽

@dataclass
class DiagnosticAttempt:
    """单次诊断尝试记录"""
    attempt_number: int
    timestamp: datetime
    diagnosis: str
    solution: str
    commands: List[str]
    execution_results: List[Dict]
    success: bool
    error_message: Optional[str] = None

@dataclass
class ContinuousDiagnosticSession:
    """持续诊断会话"""
    session_id: str
    service_name: str
    server_ip: str
    initial_error: str
    status: DiagnosticStatus
    start_time: datetime
    attempts: List[DiagnosticAttempt] = field(default_factory=list)
    max_attempts: int = 10
    current_attempt: int = 0
    final_result: Optional[str] = None
    resolution_time: Optional[datetime] = None
    
    def is_active(self) -> bool:
        """检查会话是否仍在活跃状态"""
        return self.status in [DiagnosticStatus.PENDING, DiagnosticStatus.DIAGNOSING, DiagnosticStatus.EXECUTING]

class DiagnosticAgent:
    """增强的AI诊断代理，支持持续诊断直到问题解决"""
    
    def __init__(self):
        self.active_sessions: Dict[str, ContinuousDiagnosticSession] = {}
        self.diagnostic_history = []
        self.global_attempt_strategies = [
            "restart_service",      # 重启服务
            "check_dependencies",   # 检查依赖
            "clean_rebuild",       # 清理重建
            "resource_check",      # 资源检查
            "config_validation",   # 配置验证
            "network_diagnosis",   # 网络诊断
            "rollback_attempt",    # 回滚尝试
            "emergency_recovery"   # 紧急恢复
        ]
    
    async def start_continuous_diagnosis(self, service_name: str, server_ip: str, 
                                       error_info: str, max_attempts: int = 10) -> str:
        """开始持续诊断会话"""
        session_id = f"{service_name}_{server_ip}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        session = ContinuousDiagnosticSession(
            session_id=session_id,
            service_name=service_name,
            server_ip=server_ip,
            initial_error=error_info,
            status=DiagnosticStatus.PENDING,
            start_time=datetime.now(),
            max_attempts=max_attempts
        )
        
        self.active_sessions[session_id] = session
        logger.info(f"启动持续诊断会话: {session_id}")
        
        # 发送企业微信通知（诊断开始）
        asyncio.create_task(wechat_mcp_client.send_diagnosis_update(
            session_id, service_name, server_ip, "starting", "开始持续诊断", 0
        ))
        
        # 立即开始第一次诊断
        asyncio.create_task(self._continuous_diagnosis_loop(session_id))
        
        return session_id
    
    async def _continuous_diagnosis_loop(self, session_id: str):
        """持续诊断循环"""
        session = self.active_sessions.get(session_id)
        if not session:
            return
        
        logger.info(f"开始持续诊断循环: {session_id}")
        
        while session.is_active() and session.current_attempt < session.max_attempts:
            try:
                session.status = DiagnosticStatus.DIAGNOSING
                session.current_attempt += 1
                
                logger.info(f"会话 {session_id} - 第 {session.current_attempt} 次尝试")
                
                # 执行单次诊断尝试
                attempt_result = await self._perform_single_diagnosis_attempt(session)
                session.attempts.append(attempt_result)
                
                # 检查是否成功解决
                if attempt_result.success:
                    # 验证服务是否真正恢复
                    if await self._verify_service_recovery(session):
                        session.status = DiagnosticStatus.RESOLVED
                        session.resolution_time = datetime.now()
                        session.final_result = f"问题已在第 {session.current_attempt} 次尝试中解决"
                        logger.info(f"会话 {session_id} - 问题已解决！")
                        break
                    else:
                        logger.warning(f"会话 {session_id} - 命令执行成功但服务仍有问题，继续诊断")
                
                # 等待一段时间再进行下次尝试
                await asyncio.sleep(30)  # 30秒间隔
                
            except Exception as e:
                logger.error(f"会话 {session_id} - 诊断循环出错: {e}")
                # 记录错误但继续尝试
                error_attempt = DiagnosticAttempt(
                    attempt_number=session.current_attempt,
                    timestamp=datetime.now(),
                    diagnosis=f"诊断过程出错: {str(e)}",
                    solution="系统错误，将尝试其他方案",
                    commands=[],
                    execution_results=[],
                    success=False,
                    error_message=str(e)
                )
                session.attempts.append(error_attempt)
                await asyncio.sleep(15)  # 错误后短暂等待
        
        # 诊断循环结束
        if session.status != DiagnosticStatus.RESOLVED:
            if session.current_attempt >= session.max_attempts:
                session.status = DiagnosticStatus.EXHAUSTED
                session.final_result = f"已尝试 {session.max_attempts} 次，所有方案均已用尽"
            else:
                session.status = DiagnosticStatus.FAILED
                session.final_result = "诊断过程异常终止"
        
        # 生成最终报告
        await self._generate_final_report(session)
        logger.info(f"会话 {session_id} 结束，状态: {session.status.value}")
        
        # 发送诊断完成通知
        success = session.status == DiagnosticStatus.RESOLVED
        await wechat_mcp_client.send_diagnosis_update(
            session.session_id,
            session.service_name,
            session.server_ip,
            session.status.value,
            session.final_result,
            session.current_attempt
        )
    
    async def _perform_single_diagnosis_attempt(self, session: ContinuousDiagnosticSession) -> DiagnosticAttempt:
        """执行单次诊断尝试（支持MCP工具调用）"""
        attempt_start = datetime.now()
        
        try:
            # 1. 发送诊断开始通知
            await wechat_mcp_client.send_diagnosis_update(
                session.session_id,
                session.service_name,
                session.server_ip,
                "diagnosing",
                f"开始第 {session.current_attempt} 次诊断尝试",
                session.current_attempt
            )
            
            # 2. 使用SSH MCP客户端获取服务列表和基础信息
            services_result = await ssh_mcp_client.list_services()
            
            # 查找指定的服务
            service_info = None
            if services_result.get("success", False) and "services" in services_result:
                for service in services_result["services"]:
                    if service["name"] == session.service_name:
                        service_info = service
                        break
            
            if not service_info:
                await wechat_mcp_client.send_system_alert(
                    "error", session.service_name, session.server_ip,
                    "未找到服务配置信息", "high"
                )
                return DiagnosticAttempt(
                    attempt_number=session.current_attempt,
                    timestamp=attempt_start,
                    diagnosis="未找到服务配置信息",
                    solution="请检查service_info.txt文件",
                    commands=[],
                    execution_results=[],
                    success=False,
                    error_message="服务信息不存在"
                )
            
            # 3. 收集诊断所需信息
            status_info, logs = await self._collect_service_info(
                session.server_ip, service_info, session.service_name
            )
            
            # 4. 构建诊断上下文
            diagnosis_context = self._build_diagnosis_context(session, status_info, logs, service_info)
            
            # 5. 调用AI进行智能诊断（支持MCP工具）
            diagnostic_response = await aiops_client.diagnose_error_with_context(
                session.service_name, 
                session.server_ip, 
                session.initial_error,
                diagnosis_context,
                session.current_attempt
            )
            
            # 记录LLM返回的原始诊断响应
            if config.ENABLE_DETAILED_DIAGNOSIS_LOG:
                if diagnostic_response:
                    logger.info(f"会话 {session.session_id} - 第 {session.current_attempt} 次尝试 - LLM诊断响应: {diagnostic_response[:500]}...")
                else:
                    logger.warning(f"会话 {session.session_id} - 第 {session.current_attempt} 次尝试 - LLM诊断无响应")
            
            if not diagnostic_response:
                # 如果AI完全无法响应，才使用后备策略
                logger.warning(f"AI诊断无响应，使用后备策略")
                return await self._use_fallback_strategy(session, service_info)
            
            # 6. 解析AI的诊断结果和工具调用
            try:
                diagnostic_data = json.loads(diagnostic_response)
                
                # 记录解析后的诊断信息
                if config.ENABLE_DETAILED_DIAGNOSIS_LOG:
                    logger.info(f"会话 {session.session_id} - 第 {session.current_attempt} 次尝试 - 诊断信息: {diagnostic_data.get('diagnosis', '无诊断信息')}")
                    logger.info(f"会话 {session.session_id} - 第 {session.current_attempt} 次尝试 - 解决方案: {diagnostic_data.get('solution', '无解决方案')}")
                
                # 7. 执行AI指定的MCP工具调用
                mcp_execution_results = []
                tool_calls = diagnostic_data.get("tool_calls", [])
                
                if tool_calls:
                    session.status = DiagnosticStatus.EXECUTING
                    logger.info(f"开始执行 {len(tool_calls)} 个MCP工具调用")
                    mcp_execution_results = await self._execute_mcp_tool_calls(tool_calls, session)
                else:
                    logger.warning("AI未返回MCP工具调用")
                
                # 8. 评估执行结果
                success = len(mcp_execution_results) > 0 and all(r.get("success", False) for r in mcp_execution_results)
                
                # 9. 发送执行结果通知
                if success:
                    await wechat_mcp_client.send_system_alert(
                        "success", session.service_name, session.server_ip,
                        f"第 {session.current_attempt} 次诊断尝试成功", "low"
                    )
                else:
                    await wechat_mcp_client.send_system_alert(
                        "warning", session.service_name, session.server_ip,
                        f"第 {session.current_attempt} 次诊断尝试部分失败", "medium"
                    )

                return DiagnosticAttempt(
                    attempt_number=session.current_attempt,
                    timestamp=attempt_start,
                    diagnosis=diagnostic_data.get("diagnosis", "无诊断信息"),
                    solution=diagnostic_data.get("solution", "无解决方案"),
                    commands=[f"MCP工具调用: {len(tool_calls)}个"] if tool_calls else [],
                    execution_results=mcp_execution_results,
                    success=success
                )

            except json.JSONDecodeError:
                logger.warning(f"会话 {session.session_id} - 第 {session.current_attempt} 次尝试 - JSON解析失败，使用后备处理")
                # 如果JSON解析失败，使用后备策略
                return await self._use_fallback_strategy(session, service_info)

        except Exception as e:
            logger.error(f"第 {session.current_attempt} 次诊断尝试失败: {e}")
            return DiagnosticAttempt(
                attempt_number=session.current_attempt,
                timestamp=attempt_start,
                diagnosis="诊断过程发生未知异常",
                solution="检查系统日志",
                commands=[],
                execution_results=[],
                success=False,
                error_message=str(e)
            )
    
    async def _execute_mcp_tool_calls(self, tool_calls: List[Dict], session: ContinuousDiagnosticSession) -> List[Dict]:
        """执行MCP工具调用"""
        results = []
        
        for i, tool_call in enumerate(tool_calls):
            tool = tool_call.get("tool", "")
            action = tool_call.get("action", "")
            parameters = tool_call.get("parameters", {})
            reason = tool_call.get("reason", "")
            
            logger.info(f"执行工具调用 {i+1}/{len(tool_calls)}: {tool}.{action} - {reason}")
            
            try:
                # SSH MCP工具调用
                if tool.startswith("ssh") or action in ["execute_remote_command", "execute_service_operation", 
                                                      "get_service_status", "get_service_logs", "list_services"]:
                    result = await self._execute_ssh_mcp_tool(action, parameters, session)
                    results.append({
                        "tool": f"{tool}.{action}",
                        "parameters": parameters,
                        "result": result,
                        "success": result.get("success", False),
                        "reason": reason
                    })
                
                # 微信MCP工具调用
                elif tool.startswith("wechat") or action in ["send_text_message", "send_markdown_message", 
                                                           "send_system_alert", "send_diagnosis_update"]:
                    result = await self._execute_wechat_mcp_tool(action, parameters, session)
                    results.append({
                        "tool": f"{tool}.{action}",
                        "parameters": parameters,
                        "result": {"success": result},
                        "success": result,
                        "reason": reason
                    })
                
                else:
                    logger.warning(f"未知的工具调用: {tool}.{action}")
                    results.append({
                        "tool": f"{tool}.{action}",
                        "parameters": parameters,
                        "result": {"error": "未知工具"},
                        "success": False,
                        "reason": reason
                    })
                    
            except Exception as e:
                logger.error(f"工具调用失败: {tool}.{action} - {e}")
                results.append({
                    "tool": f"{tool}.{action}",
                    "parameters": parameters,
                    "result": {"error": str(e)},
                    "success": False,
                    "reason": reason
                })
        
        return results
    
    async def _execute_ssh_mcp_tool(self, action: str, parameters: Dict, session: ContinuousDiagnosticSession) -> Dict:
        """执行SSH MCP工具"""
        try:
            if action == "list_services":
                return await ssh_mcp_client.list_services()
            
            elif action == "get_service_status":
                service_name = parameters.get("service_name", session.service_name)
                return await ssh_mcp_client.get_service_status(service_name)
            
            elif action == "get_service_logs":
                service_name = parameters.get("service_name", session.service_name)
                lines = parameters.get("lines", 100)
                return await ssh_mcp_client.get_service_logs(service_name, lines)
            
            elif action == "execute_service_operation":
                service_name = parameters.get("service_name", session.service_name)
                operation = parameters.get("operation", "status")
                log_lines = parameters.get("log_lines", 100)
                return await ssh_mcp_client.execute_service_operation(service_name, operation, log_lines)
            
            elif action == "execute_remote_command":
                # 从服务信息中获取连接参数
                services_result = await ssh_mcp_client.list_services()
                service_info = None
                if services_result.get("success", False):
                    for service in services_result.get("services", []):
                        if service["name"] == session.service_name:
                            service_info = service
                            break
                
                if not service_info:
                    return {"success": False, "error": "服务信息不存在"}
                
                hostname = service_info.get("hostname", session.server_ip)
                username = service_info.get("username", "ubuntu")
                password = service_info.get("password", "")
                command = parameters.get("command", "")
                use_sudo = parameters.get("use_sudo", True)
                timeout = parameters.get("timeout", 30)
                
                return await ssh_mcp_client.execute_remote_command(
                    hostname, username, password, command, use_sudo, timeout
                )
            
            else:
                return {"success": False, "error": f"未知的SSH操作: {action}"}
                
        except Exception as e:
            logger.error(f"SSH MCP工具执行失败: {action} - {e}")
            return {"success": False, "error": str(e)}
    
    async def _execute_wechat_mcp_tool(self, action: str, parameters: Dict, session: ContinuousDiagnosticSession) -> bool:
        """执行微信MCP工具"""
        try:
            if action == "send_text_message":
                content = parameters.get("content", "")
                mentioned_list = parameters.get("mentioned_list", [])
                return await wechat_mcp_client.send_text_message(content, mentioned_list)
            
            elif action == "send_markdown_message":
                content = parameters.get("content", "")
                return await wechat_mcp_client.send_markdown_message(content)
            
            elif action == "send_system_alert":
                alert_type = parameters.get("alert_type", "info")
                service_name = parameters.get("service_name", session.service_name)
                server_info = parameters.get("server_info", session.server_ip)
                description = parameters.get("description", "")
                severity = parameters.get("severity", "medium")
                return await wechat_mcp_client.send_system_alert(
                    alert_type, service_name, server_info, description, severity
                )
            
            elif action == "send_diagnosis_update":
                session_id = parameters.get("session_id", session.session_id)
                service_name = parameters.get("service_name", session.service_name)
                server_info = parameters.get("server_info", session.server_ip)
                status = parameters.get("status", "in_progress")
                message = parameters.get("message", "")
                attempts = parameters.get("attempts", session.current_attempt)
                return await wechat_mcp_client.send_diagnosis_update(
                    session_id, service_name, server_info, status, message, attempts
                )
            
            else:
                logger.warning(f"未知的微信操作: {action}")
                return False
                
        except Exception as e:
            logger.error(f"微信MCP工具执行失败: {action} - {e}")
            return False
    
    def _build_diagnosis_context(self, session: ContinuousDiagnosticSession, 
                               status_info: str, logs: str, service_info: Dict[str, Any]) -> str:
        """构建用于AI诊断的上下文"""
        context = f"### 诊断上下文\n"
        context += f"- **服务名称**: {session.service_name}\n"
        context += f"- **服务器IP**: {session.server_ip}\n"
        
        # 添加详细的服务配置信息
        context += f"### 服务配置详情\n"
        context += f"- **主机**: {service_info.get('hostname')}\n"
        context += f"- **服务类型**: {service_info.get('service_type')}\n"
        context += f"- **工作目录**: {service_info.get('directory')}\n"
        if service_info.get('config_path'):
            context += f"- **配置文件**: {service_info.get('config_path')}\n"
        context += "\n"

        context += f"- **初始错误**: {session.initial_error}\n"
        context += f"- **总尝试次数**: {session.max_attempts}\n"
        context += f"- **当前尝试**: 第 {session.current_attempt} 次\n\n"

        if status_info:
            context += f"### 服务状态\n```\n{status_info}\n```\n\n"
        
        if logs:
            context += f"### 相关日志 (最近50行)\n```\n{logs}\n```\n\n"

        if session.attempts:
            context += "### 历史诊断记录\n"
            for i, attempt in enumerate(session.attempts):
                context += f"#### 第 {i+1} 次尝试 ({attempt.timestamp.strftime('%Y-%m-%d %H:%M:%S')})\n"
                context += f"- **诊断**: {attempt.diagnosis}\n"
                context += f"- **解决方案**: {attempt.solution}\n"
                context += f"- **执行结果**: {'成功' if attempt.success else '失败'}\n"
                if not attempt.success:
                    context += f"- **错误信息**: {attempt.error_message}\n"
                context += "\n"
        
        return context
    
    async def _use_fallback_strategy(self, session: ContinuousDiagnosticSession, 
                                   service_info: Dict) -> DiagnosticAttempt:
        """在AI无响应或响应格式错误时使用后备策略"""
        attempt_start = datetime.now()
        
        try:
            logger.info(f"会话 {session.session_id} - 第 {session.current_attempt} 次尝试 - 正在使用后备策略")

            # 使用SSH MCP客户端获取服务状态和日志
            status_result = await ssh_mcp_client.get_service_status(session.service_name)
            status = json.dumps(status_result, ensure_ascii=False, indent=2)
            
            logs_result = await ssh_mcp_client.get_service_logs(session.service_name, lines=50)
            logs = logs_result.get("logs", "获取日志失败")
            
            # 构建上下文，即使是后备策略，也让AI知道之前的尝试
            diagnosis_context = self._build_diagnosis_context(session, status, logs, service_info)
            
            # 调用AI生成后备策略
            fallback_response = await aiops_client.generate_fallback_plan(
                session.service_name, 
                session.server_ip, 
                session.initial_error,
                diagnosis_context,
                session.current_attempt
            )
            
            # 解析和执行后备计划
            try:
                diagnostic_data = json.loads(fallback_response)
                
                if config.ENABLE_DETAILED_DIAGNOSIS_LOG:
                    logger.info(f"会话 {session.session_id} - 后备诊断: {diagnostic_data.get('diagnosis', '无')}")
                    logger.info(f"会话 {session.session_id} - 后备解决方案: {diagnostic_data.get('solution', '无')}")
                
                mcp_execution_results = []
                tool_calls = diagnostic_data.get("tool_calls", [])
                
                if tool_calls:
                    mcp_execution_results = await self._execute_mcp_tool_calls(tool_calls, session)
                
                success = len(mcp_execution_results) > 0 and all(r.get("success", False) for r in mcp_execution_results)

                return DiagnosticAttempt(
                    attempt_number=session.current_attempt,
                    timestamp=attempt_start,
                    diagnosis=diagnostic_data.get("diagnosis", "后备策略诊断"),
                    solution=diagnostic_data.get("solution", "后备策略解决方案"),
                    commands=[f"MCP工具调用: {len(tool_calls)}个"] if tool_calls else [],
                    execution_results=mcp_execution_results,
                    success=success
                )
            except Exception as e:
                logger.error(f"后备策略执行失败: {e}")
                return DiagnosticAttempt(
                    attempt_number=session.current_attempt,
                    timestamp=attempt_start,
                    diagnosis="后备策略执行异常",
                    solution=f"错误: {e}",
                    commands=[],
                    execution_results=[],
                    success=False,
                    error_message=str(e)
                )

        except Exception as e:
            logger.error(f"后备策略生成过程失败: {e}")
            return DiagnosticAttempt(
                attempt_number=session.current_attempt,
                timestamp=attempt_start,
                diagnosis="后备策略生成异常",
                solution=f"错误: {e}",
                commands=[],
                execution_results=[],
                success=False,
                error_message=str(e)
            )
    
    async def _generate_fallback_commands(self, session: ContinuousDiagnosticSession, 
                                        service_info: Dict) -> List[str]:
        """生成后备命令（支持docker-compose和supervisor）"""
        strategy_index = min(session.current_attempt - 1, len(self.global_attempt_strategies) - 1)
        strategy = self.global_attempt_strategies[strategy_index]
        service_type = service_info.get("service_type", "docker-compose")
        directory = service_info.get("directory", "/opt")
        service_name = session.service_name
        
        if service_type == "docker-compose":
            strategy_commands = {
                "restart_service": [
                    f"sudo bash -c \"cd {directory} && docker-compose stop\"",
                    f"sudo bash -c \"cd {directory} && docker-compose start\""
                ],
                "check_dependencies": [
                    f"sudo bash -c \"cd {directory} && docker-compose ps\"",
                    "sudo docker images",
                    "df -h",
                    "free -h"
                ],
                "clean_rebuild": [
                    f"sudo bash -c \"cd {directory} && docker-compose down\"",
                    "sudo docker system prune -f",
                    f"sudo bash -c \"cd {directory} && docker-compose up -d\""
                ],
                "resource_check": [
                    "sudo docker stats --no-stream",
                    "sudo docker system df",
                    f"sudo bash -c \"cd {directory} && docker-compose logs --tail=50\""
                ],
                "config_validation": [
                    f"sudo bash -c \"cd {directory} && docker-compose config\"",
                    f"sudo bash -c \"cd {directory} && docker-compose ps\"",
                    f"sudo bash -c \"cd {directory} && docker-compose logs --tail=100\""
                ],
                "network_diagnosis": [
                    "sudo docker network ls",
                    f"sudo bash -c \"cd {directory} && docker-compose down\"",
                    "sudo docker network prune -f",
                    f"sudo bash -c \"cd {directory} && docker-compose up -d\""
                ],
                "rollback_attempt": [
                    f"sudo bash -c \"cd {directory} && docker-compose down\"",
                    f"bash -c \"cd {directory} && git status\"",
                    f"sudo bash -c \"cd {directory} && docker-compose up -d\""
                ],
                "emergency_recovery": [
                    f"sudo bash -c \"cd {directory} && docker-compose down --remove-orphans\"",
                    "sudo docker system prune -af",
                    "sudo docker volume prune -f",
                    f"sudo bash -c \"cd {directory} && docker-compose pull\"",
                    f"sudo bash -c \"cd {directory} && docker-compose up -d\""
                ]
            }
            default_commands = [f"sudo bash -c \"cd {directory} && docker-compose restart\""]
            
        elif service_type == "supervisor":
            # 使用正确的supervisorctl路径
            supervisorctl_cmd = "sudo /home/<USER>/anaconda3/bin/supervisorctl"
            
            strategy_commands = {
                "restart_service": [
                    f"{supervisorctl_cmd} stop {service_name}",
                    f"{supervisorctl_cmd} start {service_name}"
                ],
                "check_dependencies": [
                    f"{supervisorctl_cmd} status {service_name}",
                    f"{supervisorctl_cmd} status",
                    "ps aux | grep python",
                    "df -h",
                    "free -h"
                ],
                "clean_rebuild": [
                    f"{supervisorctl_cmd} stop {service_name}",
                    f"{supervisorctl_cmd} reread",
                    f"{supervisorctl_cmd} update",
                    f"{supervisorctl_cmd} start {service_name}"
                ],
                "resource_check": [
                    f"{supervisorctl_cmd} status {service_name}",
                    f"sudo tail -n 100 /var/log/supervisor/{service_name}.log",
                    "ps aux | grep supervisor",
                    "df -h",
                    "free -h"
                ],
                "config_validation": [
                    f"{supervisorctl_cmd} status {service_name}",
                    f"{supervisorctl_cmd} reread",
                    f"sudo tail -n 200 /var/log/supervisor/{service_name}.log"
                ],
                "network_diagnosis": [
                    f"{supervisorctl_cmd} status {service_name}",
                    "netstat -tlnp",
                    f"{supervisorctl_cmd} restart {service_name}"
                ],
                "rollback_attempt": [
                    f"{supervisorctl_cmd} stop {service_name}",
                    f"bash -c \"cd {directory} && git status\"",
                    f"{supervisorctl_cmd} start {service_name}"
                ],
                "emergency_recovery": [
                    f"{supervisorctl_cmd} stop {service_name}",
                    "sudo pkill -f supervisor",
                    "sudo systemctl restart supervisor",
                    f"{supervisorctl_cmd} start {service_name}"
                ]
            }
            default_commands = [f"{supervisorctl_cmd} restart {service_name}"]
        else:
            # 未支持的服务类型，返回基本命令
            return [f"echo '不支持的服务类型: {service_type}'"]
        
        return strategy_commands.get(strategy, default_commands)

    async def _verify_service_recovery(self, session: ContinuousDiagnosticSession) -> bool:
        """验证服务是否真正恢复"""
        try:
            # 等待服务启动
            await asyncio.sleep(10)
            
            # 使用SSH MCP客户端获取服务状态
            status_result = await ssh_mcp_client.get_service_status(session.service_name)
            
            # 检查是否正在运行
            is_running = status_result.get("success", False)
            
            if is_running:
                # 进一步验证 - 检查最新日志是否有错误
                logs_result = await ssh_mcp_client.get_service_logs(session.service_name, lines=10)
                
                if logs_result.get("success", False):
                    logs = logs_result.get("stdout", "")
                    
                    # 简单的错误检测
                    error_indicators = ["error", "failed", "exception", "traceback", "fatal"]
                    recent_errors = any(indicator.lower() in logs.lower() for indicator in error_indicators)
                    
                    return not recent_errors
            
            return False
            
        except Exception as e:
            logger.error(f"验证服务恢复时出错: {e}")
            return False

    async def _generate_final_report(self, session: ContinuousDiagnosticSession):
        """生成最终诊断报告"""
        report = {
            "session_id": session.session_id,
            "service_name": session.service_name,
            "server_ip": session.server_ip,
            "initial_error": session.initial_error,
            "status": session.status.value,
            "start_time": session.start_time.isoformat(),
            "resolution_time": session.resolution_time.isoformat() if session.resolution_time else None,
            "total_attempts": len(session.attempts),
            "final_result": session.final_result,
            "attempts": [
                {
                    "attempt": attempt.attempt_number,
                    "timestamp": attempt.timestamp.isoformat(),
                    "diagnosis": attempt.diagnosis,
                    "solution": attempt.solution,
                    "commands": attempt.commands,
                    "success": attempt.success,
                    "error": attempt.error_message
                }
                for attempt in session.attempts
            ]
        }
        
        # 保存到历史记录
        self.diagnostic_history.append(report)
        
        # 记录日志
        logger.info(f"最终报告生成完成: {session.session_id}")
        logger.info(f"报告摘要: {session.final_result}")
    
    def get_session_status(self, session_id: str) -> Optional[Dict]:
        """获取诊断会话状态"""
        session = self.active_sessions.get(session_id)
        if not session:
            # 检查历史记录
            for history in self.diagnostic_history:
                if history.get("session_id") == session_id:
                    return history
            return None
        
        return {
            "session_id": session.session_id,
            "service_name": session.service_name,
            "server_ip": session.server_ip,
            "status": session.status.value,
            "current_attempt": session.current_attempt,
            "max_attempts": session.max_attempts,
            "start_time": session.start_time.isoformat(),
            "is_active": session.is_active(),
            "attempts_count": len(session.attempts),
            "latest_diagnosis": session.attempts[-1].diagnosis if session.attempts else None
        }
    
    def stop_session(self, session_id: str) -> bool:
        """停止诊断会话"""
        session = self.active_sessions.get(session_id)
        if session and session.is_active():
            session.status = DiagnosticStatus.FAILED
            session.final_result = "会话被手动停止"
            logger.info(f"会话 {session_id} 被手动停止")
            return True
        return False
    
    def list_active_sessions(self) -> List[Dict]:
        """列出所有活跃的诊断会话"""
        return [
            {
                "session_id": session.session_id,
                "service_name": session.service_name,
                "server_ip": session.server_ip,
                "status": session.status.value,
                "current_attempt": session.current_attempt,
                "start_time": session.start_time.isoformat()
            }
            for session in self.active_sessions.values()
            if session.is_active()
        ]

    async def _collect_service_info(self, server_ip: str, service_info: Dict[str, str], 
                                   service_name: str) -> tuple[str, str]:
        """收集服务状态和日志信息"""
        try:
            # 使用SSH MCP客户端获取服务状态
            status_result = await ssh_mcp_client.get_service_status(service_name)
            
            # 获取服务日志
            logs_result = await ssh_mcp_client.get_service_logs(service_name, lines=50)
            
            # 处理状态结果
            if status_result.get("success", False):
                status_str = json.dumps(status_result, ensure_ascii=False, indent=2)
            else:
                status_str = f"状态获取失败: {status_result.get('error', '未知错误')}"
            
            # 处理日志结果
            if logs_result.get("success", False):
                logs = logs_result.get("stdout", "无日志输出")
            else:
                logs = f"日志获取失败: {logs_result.get('error', '未知错误')}"
            
            return status_str, logs
            
        except Exception as e:
            logger.error(f"收集服务信息失败: {e}")
            return f"状态收集失败: {e}", f"日志收集失败: {e}"
    
    async def _execute_fix_commands(self, server_ip: str, service_info: Dict[str, str],
                                   commands: List[str]) -> List[Dict]:
        """执行修复命令"""
        results = []
        
        # 记录开始执行命令的日志
        if config.ENABLE_DETAILED_DIAGNOSIS_LOG:
            logger.info(f"开始执行修复命令 - 服务器: {server_ip}, 服务: {service_info.get('service_name', '未知')}")
            logger.info(f"计划执行 {len(commands)} 个命令: {commands}")
        
        try:
            for i, command in enumerate(commands, 1):
                logger.info(f"执行第 {i}/{len(commands)} 个命令: {command}")
                
                # 使用SSH MCP客户端执行远程命令
                result = await ssh_mcp_client.execute_remote_command(
                    hostname=server_ip,
                    username=service_info["username"],
                    password=service_info["password"],
                    command=command,
                    use_sudo=not command.strip().startswith("sudo"),
                    timeout=60
                )
                
                results.append(result)
                
                # 详细记录命令执行结果
                success = result.get('success', False)
                stdout = result.get('stdout', '')
                stderr = result.get('stderr', '')
                
                logger.info(f"第 {i} 个命令执行结果: {'成功' if success else '失败'}")
                if config.ENABLE_DETAILED_DIAGNOSIS_LOG:
                    if stdout:
                        logger.info(f"第 {i} 个命令标准输出: {stdout[:200]}...")
                    if stderr and not success:
                        logger.warning(f"第 {i} 个命令错误输出: {stderr[:200]}...")
                
                # 如果关键命令失败，可能需要停止后续执行
                is_critical_command = (
                    ("docker" in command.lower() and any(op in command for op in ["up", "start", "restart"])) or
                    ("supervisorctl" in command.lower() and any(op in command for op in ["start", "restart"]))
                )
                
                if not success and is_critical_command:
                    logger.warning(f"关键命令失败，停止后续执行 - 命令: {command}")
                    logger.warning(f"失败原因: {stderr or '未知错误'}")
                    # 仅在严重错误且微信通知启用时发送警报
                    asyncio.create_task(wechat_mcp_client.send_system_alert(
                        "error",
                        service_info.get("service_name", "未知服务"),
                        server_ip,
                        f"关键命令执行失败: {command}"
                    ))
                    break
                
                # 在命令之间添加短暂延迟
                await asyncio.sleep(2)
                
        except Exception as e:
            logger.error(f"执行修复命令时出错: {e}")
            results.append({
                "command": "execution_error",
                "success": False,
                "error": str(e)
            })
        
        # 记录命令执行汇总
        successful_commands = sum(1 for r in results if r.get('success', False))
        logger.info(f"命令执行完成 - 成功: {successful_commands}/{len(results)}")
        if config.ENABLE_DETAILED_DIAGNOSIS_LOG and results:
            logger.info(f"命令执行详细结果: {[{'cmd': r.get('command', '')[:50], 'success': r.get('success', False)} for r in results]}")
        
        return results
    
    async def get_service_health_check(self, service_name: str, server_ip: str) -> Dict:
        """获取服务健康检查结果"""
        try:
            # 使用SSH MCP客户端获取服务状态
            status_result = await ssh_mcp_client.get_service_status(service_name)
            
            # 获取服务日志
            logs_result = await ssh_mcp_client.get_service_logs(service_name, lines=20)
            
            # 检查服务是否异常
            is_running = status_result.get("success", False)
            if not is_running and wechat_mcp_client.enabled:
                # 仅在服务异常且启用微信通知时发送警报
                asyncio.create_task(wechat_mcp_client.send_system_alert(
                    "error",
                    service_name,
                    server_ip,
                    "服务未正常运行，健康检查失败"
                ))
            
            return {
                "success": True,
                "service_name": service_name,
                "server_ip": server_ip,
                "status": status_result,
                "recent_logs": logs_result.get("stdout", "无日志"),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def get_diagnostic_history(self, limit: int = 10) -> List[Dict]:
        """获取诊断历史记录"""
        history = self.diagnostic_history[-limit:] if limit > 0 else self.diagnostic_history
        
        return [
            {
                "session_id": item.get("session_id", "unknown"),
                "service_name": item.get("service_name", "unknown"),
                "server_ip": item.get("server_ip", "unknown"),
                "initial_error": item.get("initial_error", ""),
                "status": item.get("status", "unknown"),
                "start_time": item.get("start_time", ""),
                "total_attempts": item.get("total_attempts", 0),
                "final_result": item.get("final_result", "")
            }
            for item in history
        ]

# 全局诊断代理实例
diagnostic_agent = DiagnosticAgent() 