#!/usr/bin/env python3
"""
SSH操作MCP服务器
提供SSH连接、远程命令执行和服务管理功能的MCP工具
"""

import asyncio
import json
import os
from typing import Any, Dict, List, Optional, Tuple
from pathlib import Path

import paramiko
from mcp.server import Server, NotificationOptions
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    Tool,
    TextContent
)
from loguru import logger
from pydantic import BaseModel, Field

# 辅助函数：生成符合MCP要求的文本内容字典
def _tc(msg: str) -> TextContent:
    return TextContent(type="text", text=str(msg))


class SSHConfig(BaseModel):
    """SSH配置模型"""
    service_info_file: str = Field(default="../service_info.txt", description="服务信息文件路径")
    connection_timeout: int = Field(default=10, description="连接超时时间（秒）")
    command_timeout: int = Field(default=30, description="命令执行超时时间（秒）")


class SSHConnection:
    """SSH连接管理器"""
    
    def __init__(self):
        self.connections = {}  # 缓存SSH连接
    
    def create_connection(self, hostname: str, username: str, password: str, port: int = 22) -> paramiko.SSHClient:
        """创建SSH连接"""
        try:
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh.connect(hostname=hostname, username=username, password=password, port=port, timeout=10)
            return ssh
        except Exception as e:
            logger.error(f"SSH连接失败 {hostname}: {e}")
            raise
    
    def get_connection(self, hostname: str, username: str, password: str) -> paramiko.SSHClient:
        """获取或创建SSH连接（带缓存）"""
        connection_key = f"{username}@{hostname}"
        
        if connection_key in self.connections:
            ssh = self.connections[connection_key]
            try:
                # 测试连接是否仍然有效
                ssh.exec_command("echo test", timeout=5)
                return ssh
            except:
                # 连接已断开，移除缓存
                try:
                    ssh.close()
                except:
                    pass
                del self.connections[connection_key]
        
        # 创建新连接
        ssh = self.create_connection(hostname, username, password)
        self.connections[connection_key] = ssh
        return ssh
    
    def execute_command(self, ssh: paramiko.SSHClient, command: str, 
                       use_sudo: bool = False, timeout: int = 30) -> Tuple[int, str, str]:
        """执行远程命令"""
        try:
            if use_sudo:
                command = f"sudo {command}"
            
            logger.info(f"执行命令: {command}")
            stdin, stdout, stderr = ssh.exec_command(command, timeout=timeout)
            
            exit_status = stdout.channel.recv_exit_status()
            stdout_data = stdout.read().decode('utf-8')
            stderr_data = stderr.read().decode('utf-8')
            
            return exit_status, stdout_data, stderr_data
        except Exception as e:
            logger.error(f"命令执行失败: {e}")
            return -1, "", str(e)
    
    def close_all_connections(self):
        """关闭所有连接"""
        for connection_key, ssh in self.connections.items():
            try:
                ssh.close()
            except:
                pass
        self.connections.clear()


class ServiceInfoManager:
    """服务信息管理器"""
    
    def __init__(self, service_info_file: str):
        self.service_info_file = Path(service_info_file)
        self.service_cache = {}
    
    async def load_service_info(self) -> Dict[str, Dict[str, str]]:
        """加载服务信息"""
        if self.service_cache:
            return self.service_cache
        
        try:
            if not self.service_info_file.exists():
                logger.warning(f"服务信息文件不存在: {self.service_info_file}")
                return {}
            
            with open(self.service_info_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    parts = line.split(',')
                    if len(parts) >= 6:
                        service_name = parts[0]
                        hostname = parts[1]
                        username = parts[2]
                        password = parts[3]
                        directory = parts[4]
                        service_type = parts[5]
                        config_path = parts[6] if len(parts) > 6 else ""
                        supervisor_cmd = parts[7] if len(parts) > 7 else "/usr/bin/supervisorctl"
                        
                        self.service_cache[service_name] = {
                            "hostname": hostname,
                            "username": username,
                            "password": password,
                            "directory": directory,
                            "service_type": service_type,
                            "config_path": config_path,
                            "supervisor_cmd": supervisor_cmd
                        }
            
            logger.info(f"加载了 {len(self.service_cache)} 个服务配置")
            return self.service_cache
            
        except Exception as e:
            logger.error(f"加载服务信息失败: {e}")
            return {}
    
    async def get_service_info(self, service_name: str) -> Optional[Dict[str, str]]:
        """获取特定服务的信息"""
        services = await self.load_service_info()
        return services.get(service_name)


# 全局变量
ssh_connection: SSHConnection = SSHConnection()
service_manager: ServiceInfoManager = ServiceInfoManager("../service_info.txt")

# 创建MCP服务器
server = Server("ssh-mcp-server")


@server.list_tools()
async def handle_list_tools() -> List[Tool]:
    """列出可用的工具"""
    return [
        Tool(
            name="execute_remote_command",
            description="在远程服务器上执行命令",
            inputSchema={
                "type": "object",
                "properties": {
                    "hostname": {
                        "type": "string",
                        "description": "服务器主机名或IP地址"
                    },
                    "username": {
                        "type": "string", 
                        "description": "SSH用户名"
                    },
                    "password": {
                        "type": "string",
                        "description": "SSH密码"
                    },
                    "command": {
                        "type": "string",
                        "description": "要执行的命令"
                    },
                    "use_sudo": {
                        "type": "boolean",
                        "description": "是否使用sudo执行命令",
                        "default": False
                    },
                    "timeout": {
                        "type": "integer",
                        "description": "命令执行超时时间（秒）",
                        "default": 30
                    }
                },
                "required": ["hostname", "username", "password", "command"]
            }
        ),
        Tool(
            name="execute_service_operation",
            description="对服务执行运维操作（支持docker-compose和supervisor）",
            inputSchema={
                "type": "object",
                "properties": {
                    "service_name": {
                        "type": "string",
                        "description": "服务名称（在service_info.txt中定义）"
                    },
                    "operation": {
                        "type": "string",
                        "enum": ["start", "stop", "restart", "status", "logs", "reload"],
                        "description": "要执行的操作"
                    },
                    "log_lines": {
                        "type": "integer", 
                        "description": "获取日志时的行数",
                        "default": 100
                    }
                },
                "required": ["service_name", "operation"]
            }
        ),
        Tool(
            name="get_service_status",
            description="获取服务状态信息",
            inputSchema={
                "type": "object",
                "properties": {
                    "service_name": {
                        "type": "string",
                        "description": "服务名称（在service_info.txt中定义）"
                    }
                },
                "required": ["service_name"]
            }
        ),
        Tool(
            name="get_service_logs",
            description="获取服务日志",
            inputSchema={
                "type": "object",
                "properties": {
                    "service_name": {
                        "type": "string", 
                        "description": "服务名称（在service_info.txt中定义）"
                    },
                    "lines": {
                        "type": "integer",
                        "description": "要获取的日志行数",
                        "default": 100
                    }
                },
                "required": ["service_name"]
            }
        ),
        Tool(
            name="list_services",
            description="列出所有可用的服务",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),
        Tool(
            name="test_ssh_connection",
            description="测试SSH连接",
            inputSchema={
                "type": "object",
                "properties": {
                    "hostname": {
                        "type": "string",
                        "description": "服务器主机名或IP地址"
                    },
                    "username": {
                        "type": "string",
                        "description": "SSH用户名"
                    },
                    "password": {
                        "type": "string",
                        "description": "SSH密码"
                    }
                },
                "required": ["hostname", "username", "password"]
            }
        )
    ]


@server.call_tool()
async def handle_call_tool(name: str, arguments: dict) -> CallToolResult:
    """处理工具调用"""
    try:
        if name == "execute_remote_command":
            return await execute_remote_command(**arguments)
        elif name == "execute_service_operation":
            return await execute_service_operation(**arguments)
        elif name == "get_service_status":
            return await get_service_status(**arguments)
        elif name == "get_service_logs":
            return await get_service_logs(**arguments)
        elif name == "list_services":
            return await list_services()
        elif name == "test_ssh_connection":
            return await test_ssh_connection(**arguments)
        else:
            return CallToolResult(
                content=[_tc(f"未知工具: {name}")],
                isError=True
            )
    except Exception as e:
        logger.error(f"工具调用失败 {name}: {e}")
        return CallToolResult(
            content=[_tc(f"工具调用失败: {str(e)}")],
            isError=True
        )


async def execute_remote_command(hostname: str, username: str, password: str, 
                               command: str, use_sudo: bool = False, timeout: int = 30) -> CallToolResult:
    """执行远程命令"""
    try:
        ssh = ssh_connection.get_connection(hostname, username, password)
        exit_status, stdout, stderr = ssh_connection.execute_command(ssh, command, use_sudo, timeout)
        
        result = {
            "success": exit_status == 0,
            "exit_status": exit_status,
            "command": command,
            "stdout": stdout,
            "stderr": stderr
        }
        
        return CallToolResult(
            content=[_tc(json.dumps(result, ensure_ascii=False, indent=2))]
        )
        
    except Exception as e:
        return CallToolResult(
            content=[_tc(f"命令执行失败: {str(e)}")]
        )


async def execute_service_operation(service_name: str, operation: str, log_lines: int = 100) -> CallToolResult:
    """执行服务操作"""
    try:
        service_info = await service_manager.get_service_info(service_name)
        if not service_info:
            return CallToolResult(
                content=[_tc(f"未找到服务 '{service_name}' 的配置信息")]
            )
        
        ssh = ssh_connection.get_connection(
            service_info["hostname"], 
            service_info["username"], 
            service_info["password"]
        )
        
        if service_info["service_type"] == "docker-compose":
            result = await _execute_docker_operation(ssh, service_name, service_info, operation, log_lines)
        elif service_info["service_type"] == "supervisor":
            result = await _execute_supervisor_operation(ssh, service_name, service_info, operation, log_lines)
        else:
            return CallToolResult(
                content=[_tc(f"不支持的服务类型: {service_info['service_type']}")]
            )
        
        return CallToolResult(
            content=[_tc(json.dumps(result, ensure_ascii=False, indent=2))]
        )
        
    except Exception as e:
        return CallToolResult(
            content=[_tc(f"服务操作失败: {str(e)}")]
        )


async def _execute_docker_operation(ssh: paramiko.SSHClient, service_name: str, 
                                  service_info: Dict[str, str], operation: str, log_lines: int) -> Dict:
    """执行Docker Compose操作"""
    command_map = {
        "start": f"docker-compose up -d {service_name}",
        "stop": f"docker-compose stop {service_name}",
        "restart": f"docker-compose restart {service_name}",
        "status": f"docker-compose ps {service_name}",
        "logs": f"docker-compose logs --tail={log_lines} {service_name}",
        "reload": f"docker-compose restart {service_name}"
    }
    
    command = command_map.get(operation.lower())
    if not command:
        return {"success": False, "error": f"不支持的Docker操作: {operation}"}
    
    # 切换到工作目录并执行命令
    full_command = f"cd {service_info['directory']} && {command}"
    exit_status, stdout, stderr = ssh_connection.execute_command(ssh, full_command, use_sudo=True)
    
    return {
        "success": exit_status == 0,
        "service_type": "docker-compose",
        "service_name": service_name,
        "operation": operation,
        "command": full_command,
        "stdout": stdout,
        "stderr": stderr,
        "exit_status": exit_status
    }


async def _execute_supervisor_operation(ssh: paramiko.SSHClient, service_name: str,
                                      service_info: Dict[str, str], operation: str, log_lines: int) -> Dict:
    """执行Supervisor操作"""
    supervisor_cmd = service_info.get("supervisor_cmd", "/usr/bin/supervisorctl")
    
    command_map = {
        "start": f"{supervisor_cmd} start {service_name}",
        "stop": f"{supervisor_cmd} stop {service_name}",
        "restart": f"{supervisor_cmd} restart {service_name}",
        "status": f"{supervisor_cmd} status {service_name}",
        "logs": f"{supervisor_cmd} tail -{log_lines} {service_name}",
        "reload": f"{supervisor_cmd} reload"
    }
    
    command = command_map.get(operation.lower())
    if not command:
        return {"success": False, "error": f"不支持的Supervisor操作: {operation}"}
    
    exit_status, stdout, stderr = ssh_connection.execute_command(ssh, command, use_sudo=True)
    
    return {
        "success": exit_status == 0,
        "service_type": "supervisor",
        "service_name": service_name,
        "operation": operation,
        "command": command,
        "stdout": stdout,
        "stderr": stderr,
        "exit_status": exit_status
    }


async def get_service_status(service_name: str) -> CallToolResult:
    """获取服务状态"""
    try:
        service_info = await service_manager.get_service_info(service_name)
        if not service_info:
            return CallToolResult(
                content=[_tc(f"未找到服务 '{service_name}' 的配置信息")]
            )
        
        result = await execute_service_operation(service_name, "status")
        return result
        
    except Exception as e:
        return CallToolResult(
            content=[_tc(f"获取服务状态失败: {str(e)}")]
        )


async def get_service_logs(service_name: str, lines: int = 100) -> CallToolResult:
    """获取服务日志"""
    try:
        service_info = await service_manager.get_service_info(service_name)
        if not service_info:
            return CallToolResult(
                content=[_tc(f"未找到服务 '{service_name}' 的配置信息")]
            )
        
        result = await execute_service_operation(service_name, "logs", lines)
        return result
        
    except Exception as e:
        return CallToolResult(
            content=[_tc(f"获取服务日志失败: {str(e)}")]
        )


async def list_services() -> CallToolResult:
    """列出所有服务"""
    try:
        services = await service_manager.load_service_info()
        service_list = []

        for service_name, info in services.items():
            service_list.append({
                "name": service_name,
                "hostname": info["hostname"],
                "service_type": info["service_type"],
                "directory": info["directory"],
                "supervisor_cmd": info.get("supervisor_cmd", "")
            })

        result = {
            "success": True,
            "services": service_list,
            "total_count": len(service_list)
        }

        return CallToolResult(
            content=[_tc(json.dumps(result, ensure_ascii=False, indent=2))]
        )

    except Exception as e:
        logger.error(f"列出服务时发生错误: {e}")
        error_result = {
            "success": False,
            "error": str(e),
            "services": [],
            "total_count": 0
        }
        return CallToolResult(
            content=[_tc(json.dumps(error_result, ensure_ascii=False, indent=2))],
            isError=True
        )


async def test_ssh_connection(hostname: str, username: str, password: str) -> CallToolResult:
    """测试SSH连接"""
    try:
        ssh = ssh_connection.get_connection(hostname, username, password)
        exit_status, stdout, stderr = ssh_connection.execute_command(ssh, "echo 'SSH连接测试成功'")
        
        result = {
            "success": exit_status == 0,
            "hostname": hostname,
            "username": username,
            "message": "SSH连接测试成功" if exit_status == 0 else "SSH连接测试失败",
            "stdout": stdout,
            "stderr": stderr
        }
        
        return CallToolResult(
            content=[_tc(json.dumps(result, ensure_ascii=False, indent=2))]
        )
        
    except Exception as e:
        return CallToolResult(
            content=[_tc(f"SSH连接测试失败: {str(e)}")]
        )


async def main():
    """主函数"""
    try:
        # 初始化日志
        logger.add("ssh_mcp.log", rotation="10 MB", retention="7 days")
        logger.info("SSH MCP服务器启动")
        
        # 运行服务器
        async with stdio_server() as (read_stream, write_stream):
            await server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="ssh-mcp-server",
                    server_version="1.0.0",
                    capabilities=server.get_capabilities(
                        notification_options=NotificationOptions(
                            tools_changed=True,
                            prompts_changed=False,
                            resources_changed=False
                        ),
                        experimental_capabilities={}
                    ),
                ),
            )
    except Exception as e:
        logger.error(f"SSH MCP服务器启动失败: {e}")
        raise
    finally:
        # 清理连接
        ssh_connection.close_all_connections()


if __name__ == "__main__":
    asyncio.run(main()) 