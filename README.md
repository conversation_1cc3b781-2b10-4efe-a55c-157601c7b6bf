# AI运维代理 (AI Ops Agent)

一个基于AI的智能Docker服务运维代理，能够自动诊断和修复服务问题。

## 功能特性

- 🤖 **AI持续诊断**: 使用本地AI模型进行智能诊断，持续尝试直到问题解决
- 🔄 **智能重试机制**: 自动避免重复失败的方案，从多角度分析问题
- 📋 **诊断会话管理**: 支持多个并发诊断会话，实时监控进度
- 🎯 **渐进式修复策略**: 从简单重启到深度系统修复的8种预设策略
- 🐳 **统一服务管理**: 支持Docker Compose和Supervisor两种服务类型的统一管理
- **SSH远程操作**: 安全连接到远程服务器执行Docker操作
- 📡 **RESTful API**: 提供完整的API接口，支持外部系统集成
- 📊 **健康检查**: 实时监控服务状态和日志
- 📝 **详细操作记录**: 记录每次尝试的诊断、命令和结果
- 🔒 **权限管理**: 支持sudo权限提升进行Docker操作
- ⚡ **异步处理**: 后台运行诊断任务，不阻塞API响应

## 架构设计

本系统采用完全本地化的模块化设计，主要组件包括：

- **本地AI模型客户端**: 完全基于本地vLLM服务，负责智能诊断和命令生成
- **SSH客户端**: 远程连接和命令执行
- **诊断代理**: 核心诊断和修复逻辑
- **API服务器**: RESTful接口层

### 本地化优势

- **完全离线**: 无需外部API依赖，保证数据安全和隐私
- **专业定制**: 基于Docker运维专家角色设计的专业prompt
- **渐进式诊断**: 多层次、多角度的故障分析策略
- **成本控制**: 无API调用费用，长期运行成本低

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```



### 2. 配置环境变量

创建 `.env` 文件并配置以下参数：

```env
# 本地vLLM配置（主要AI服务）
VLLM_BASE_URL=http://localhost:8000/v1
VLLM_MODEL=your_local_model_name
VLLM_API_KEY=dummy_key

# API服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# 服务信息文件路径
SERVICE_INFO_FILE=service_info.txt

# 日志级别
LOG_LEVEL=INFO

# 企业微信机器人配置（可选）
WECHAT_BOT_URL=your_wechat_bot_webhook_url
WECHAT_BOT=FALSE
```

### 2.1 启动本地vLLM服务

在使用本系统前，需要先启动本地vLLM服务：

```bash
# 安装vLLM
pip install vllm

# 启动vLLM服务（以Qwen2.5-7B-Instruct为例）
python -m vllm.entrypoints.openai.api_server \
    --model Qwen/Qwen2.5-7B-Instruct \
    --served-model-name local_model \
    --host localhost \
    --port 8000
```

### 3. 配置服务信息

编辑 `service_info.txt` 文件，新格式为：
```
服务名,主机名,用户名,密码,目录路径,服务类型,配置路径
```

示例：
```
dify,ubuntu,ubuntu,tf$Ke^HB5lm&,/home/<USER>/workspace/dify-main/docker/,docker-compose,
ragflow,ubuntu,ubuntu,tf$Ke^HB5lm&,/home/<USER>/workspace/ragflow-main/docker/,docker-compose,
ocr,ubuntu,ubuntu,tf$Ke^HB5lm&,/home/<USER>/workspace/ocr_0205,supervisor,/etc/supervisor/conf.d/
```

支持的服务类型：
- `docker-compose`: Docker Compose管理的服务
- `supervisor`: Supervisor管理的Python进程

### 4. 启动服务

```bash
python main.py
```

服务启动后将在配置的端口上提供API服务。

## API接口

## 使用方式

现在只需要调用：
```python
# 启动持续诊断
session_id = await diagnostic_agent.start_continuous_diagnosis(
    service_name="ragflow",
    server_ip="***********", 
    error_info="服务故障",
    max_attempts=10
)

# 跟踪进度
status = diagnostic_agent.get_session_status(session_id)
```

或通过API：
```bash
# 报告错误（自动启动持续诊断）
POST /diagnose
{
    "service_name": "ragflow",
    "server_ip": "***********",
    "error_info": "服务故障"
}

# 查看进度
GET /session/{session_id}
``` 
#### 3. 停止诊断会话

**POST** `/session/stop`

```json
{
    "session_id": "dify_*************_20241201_143022"
}
```

#### 4. 列出活跃会话

**GET** `/sessions`

#### 6. 健康检查

**POST** `/health-check`

```json
{
    "service_name": "dify",
    "server_ip": "*************"
}
```

#### 7. 获取服务列表

**GET** `/services`

#### 8. 获取诊断历史

**GET** `/history?limit=10`

#### 9. 手动执行命令

**POST** `/manual-command`

```json
{
    "service_name": "dify",
    "server_ip": "*************",
    "commands": ["docker-compose ps", "docker-compose logs --tail=20"],
    "use_sudo": true
}
```

#### 10. 获取服务状态

**GET** `/status/{service_name}/{server_ip}`

#### 11. 统一服务操作 (新功能)

**POST** `/service/operation`

支持Docker Compose和Supervisor服务的统一操作：

```json
{
    "service_name": "dify",
    "operation": "start",  // start, stop, restart, status, logs
    "lines": 100           // 仅用于logs操作
}
```

支持的操作：
- `start`: 启动服务
- `stop`: 停止服务  
- `restart`: 重启服务
- `status`: 查询状态
- `logs`: 获取日志

#### 12. 获取服务配置信息

**GET** `/service/{service_name}/info`

返回服务的配置信息（不包含密码）

## 使用示例

### 🆕 持续诊断使用示例

#### 通过curl启动持续诊断

```bash
# 1. 启动持续诊断
curl -X POST "http://localhost:8080/continuous-diagnosis" \
     -H "Content-Type: application/json" \
     -d '{
       "service_name": "dify",
       "server_ip": "*************", 
       "error_info": "容器反复重启，无法正常提供服务",
       "max_attempts": 8
     }'

# 响应示例:
# {
#   "success": true,
#   "data": {
#     "session_id": "dify_*************_20241201_143022"
#   }
# }

# 2. 监控诊断进度
curl "http://localhost:8080/session/dify_*************_20241201_143022"

# 3. 列出所有活跃会话
curl "http://localhost:8080/sessions"
```

#### Python持续诊断客户端

```python
import asyncio
import httpx

async def continuous_diagnosis_example():
    async with httpx.AsyncClient() as client:
        # 1. 启动持续诊断
        response = await client.post('http://localhost:8080/continuous-diagnosis', json={
            'service_name': 'dify',
            'server_ip': '*************',
            'error_info': '服务无法启动，容器状态异常',
            'max_attempts': 5
        })
        
        session_id = response.json()['data']['session_id']
        print(f"诊断会话已启动: {session_id}")
        
        # 2. 监控进度
        while True:
            status_response = await client.get(f'http://localhost:8080/session/{session_id}')
            status = status_response.json()['data']
            
            print(f"状态: {status['status']}, 尝试: {status.get('current_attempt', 0)}")
            
            if not status.get('is_active', False):
                print(f"诊断完成！最终状态: {status['status']}")
                break
            
            await asyncio.sleep(10)  # 等待10秒再检查

# 运行示例
asyncio.run(continuous_diagnosis_example())
```

#### 使用提供的客户端工具

```bash
# 快速测试
python continuous_diagnosis_example.py quick

# 完整演示
python continuous_diagnosis_example.py
```

### 🆕 统一服务操作示例

#### 通过curl调用服务操作接口

```bash
# 查看服务状态
curl -X POST "http://localhost:8080/service/operation" \
     -H "Content-Type: application/json" \
     -d '{
       "service_name": "dify",
       "operation": "status"
     }'

# 重启服务
curl -X POST "http://localhost:8080/service/operation" \
     -H "Content-Type: application/json" \
     -d '{
       "service_name": "ocr",
       "operation": "restart"
     }'

# 获取服务日志（最近50行）
curl -X POST "http://localhost:8080/service/operation" \
     -H "Content-Type: application/json" \
     -d '{
       "service_name": "ragflow",
       "operation": "logs",
       "lines": 50
     }'

# 获取服务配置信息
curl -X GET "http://localhost:8080/service/dify/info"
```

#### 使用Python示例脚本

```bash
# 运行统一服务操作演示
python example_service_operations.py

# 测试特定服务操作
python -c "
from example_service_operations import test_specific_operation
test_specific_operation('ocr', 'status')
test_specific_operation('dify', 'logs')
"
```

#### Python客户端示例

```python
import requests

# 1. 获取服务信息
response = requests.get('http://localhost:8080/service/dify/info')
service_info = response.json()
print(f"服务类型: {service_info['data']['service_info']['service_type']}")

# 2. 查看服务状态
response = requests.post('http://localhost:8080/service/operation', json={
    'service_name': 'dify',
    'operation': 'status'
})
status_result = response.json()
print(f"服务运行状态: {status_result['data']['status_info']['running']}")

# 3. 获取服务日志
response = requests.post('http://localhost:8080/service/operation', json={
    'service_name': 'ocr',
    'operation': 'logs',
    'lines': 20
})
logs_result = response.json()
print(f"服务日志:\n{logs_result['data']['logs']}")

# 4. 重启服务（谨慎操作）
# response = requests.post('http://localhost:8080/service/operation', json={
#     'service_name': 'dify',
#     'operation': 'restart'
# })
```

### 传统单次诊断示例

#### 通过curl调用诊断接口

```bash
curl -X POST "http://localhost:8080/diagnose" \
     -H "Content-Type: application/json" \
     -d '{
       "service_name": "dify",
       "server_ip": "*************", 
       "error_info": "容器启动失败，端口冲突",
       "auto_fix": true
     }'
```

#### Python客户端示例

```python
import requests

# 发送错误报告
response = requests.post('http://localhost:8080/diagnose', json={
    'service_name': 'dify',
    'server_ip': '*************',
    'error_info': '服务响应超时',
    'auto_fix': True
})

print(response.json())
```

## 配置说明

### 环境变量配置

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `VLLM_BASE_URL` | 本地vLLM服务URL | http://localhost:8000/v1 |
| `VLLM_MODEL` | 本地模型名称 | local_model |
| `VLLM_API_KEY` | vLLM API密钥 | dummy_key |
| `SERVER_HOST` | API服务器监听地址 | 0.0.0.0 |
| `SERVER_PORT` | API服务器端口 | 8080 |
| `SERVICE_INFO_FILE` | 服务信息文件路径 | service_info.txt |
| `LOG_LEVEL` | 日志级别 | INFO |
| `WECHAT_BOT_URL` | 企业微信机器人URL | 空 |
| `WECHAT_BOT` | 是否启用微信推送 | FALSE |

### 推荐的本地模型

本系统经过优化，适配以下本地模型：

- **Qwen2.5-7B-Instruct**: 阿里云千问模型，中文理解能力强
- **ChatGLM3-6B**: 清华智谱模型，对话能力优秀  
- **Baichuan2-7B-Chat**: 百川模型，中文专项优化
- **Yi-6B-Chat**: 零一万物模型，推理能力强

### Docker运维专家Prompt设计

本系统基于专业的角色设定和prompt工程，为Docker运维场景专门优化：

#### 核心特性
- **角色化设计**: 模拟资深Docker运维专家的思维模式
- **结构化输出**: 严格的JSON格式，便于程序处理
- **渐进式策略**: 根据尝试次数采用不同的诊断维度
- **多角度分析**: 从容器、网络、存储、配置等多层面诊断

#### 诊断维度
1. **第1次**: 基础服务重启和状态检查
2. **第2次**: 配置和环境变量检查
3. **第3次**: 网络和端口问题排查
4. **第4次**: 存储和权限问题检查
5. **第5次**: 依赖服务和资源问题
6. **第6次**: 深层系统问题和完全重建
7. **第7次**: 备用方案和降级处理

### 服务信息文件格式

服务信息文件每行包含一个服务的配置信息，格式为：
```
服务名,SSH用户名,SSH密码,Docker工作目录
```

### 自动修复策略

系统根据问题优先级决定是否自动执行修复：
- `urgent`: 立即自动修复
- `high`: 立即自动修复  
- `medium`: 仅提供修复建议
- `low`: 仅提供修复建议

## 日志管理

系统采用loguru进行日志管理：
- 控制台输出：彩色格式化日志
- 文件日志：存储在 `logs/app.log`
- 自动轮转：单文件最大10MB
- 保留策略：保留7天的日志文件

## 安全考虑

1. **SSH密钥认证**: 建议使用SSH密钥代替密码认证
2. **网络隔离**: 将API服务部署在内网环境
3. **权限控制**: 确保SSH用户具有适当的sudo权限
4. **日志脱敏**: 敏感信息会在日志中被遮蔽

## 故障排除

### 常见问题

1. **本地模型连接失败**
   - 确认vLLM服务是否正常启动
   - 检查端口8000是否被占用
   - 验证模型加载是否成功

2. **SSH连接失败**
   - 确认服务器信息配置正确
   - 检查SSH服务是否运行
   - 验证用户名和密码

3. **Docker命令执行失败**
   - 确认用户具有sudo权限
   - 检查Docker服务是否运行
   - 验证工作目录路径

4. **模型响应异常**
   - 检查模型是否支持中文指令
   - 确认模型大小是否足够（建议7B以上）
   - 验证vLLM版本兼容性

### 调试模式

启用详细日志输出：
```bash
export LOG_LEVEL=DEBUG
python main.py
```

## 开发和扩展

### 添加新的诊断规则

在 `diagnostic_agent.py` 中扩展诊断逻辑：

```python
async def custom_diagnostic_rule(self, service_name: str, error_info: str):
    # 自定义诊断逻辑
    pass
```

### 集成新的模型提供商

在 `model_client.py` 中添加新的客户端类：

```python
class CustomModelClient:
    async def chat_completion(self, messages):
        # 自定义模型实现
        pass
```

## 许可证

MIT License

## 贡献

欢迎提交问题和功能请求。请确保遵循代码规范并添加适当的测试。 