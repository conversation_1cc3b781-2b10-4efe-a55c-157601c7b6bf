# SSH MCP 服务

这是一个基于MCP（Model Control Protocol）的SSH操作服务，提供SSH连接、远程命令执行和服务管理功能。

## 功能特性

- **SSH连接管理**: 支持SSH连接的创建、缓存和重用
- **远程命令执行**: 在远程服务器上执行任意命令
- **服务管理**: 支持Docker Compose和Supervisor服务的启动、停止、重启等操作
- **日志查看**: 获取服务日志
- **状态监控**: 获取服务运行状态
- **配置管理**: 从service_info.txt文件中读取服务配置

## 安装依赖

```bash
pip install -r requirements.txt
```

## 服务配置格式

在`service_info.txt`文件中，每行代表一个服务的配置信息，格式如下：

```
服务名,主机名,用户名,密码,工作目录,服务类型,配置文件路径,supervisor命令路径
```

例如：
```
dify,***********,ubuntu,tf$Ke^HB5lm&,/home/<USER>/workspace/dify-main/docker/,docker-compose,,
ragflow,***********,ubuntu,tf$Ke^HB5lm&,/home/<USER>/workspace/ragflow-main/docker/,docker-compose,,
ocr_1111_test,***********,ubuntu,tf$Ke^HB5lm&,/home/<USER>/workspace/ocr_0205,supervisor,/etc/supervisor/conf.d/ocr_5106_for_ops_test.conf,/home/<USER>/anaconda3/bin/supervisorctl
```

## MCP工具列表

### execute_remote_command
在远程服务器上执行命令

参数：
- `hostname`: 服务器主机名或IP地址
- `username`: SSH用户名
- `password`: SSH密码
- `command`: 要执行的命令
- `use_sudo`: 是否使用sudo执行命令（默认false）
- `timeout`: 命令执行超时时间（默认30秒）

### execute_service_operation
对服务执行运维操作

参数：
- `service_name`: 服务名称（在service_info.txt中定义）
- `operation`: 操作类型（start/stop/restart/status/logs/reload）
- `log_lines`: 获取日志时的行数（默认100）

### get_service_status
获取服务状态信息

参数：
- `service_name`: 服务名称

### get_service_logs
获取服务日志

参数：
- `service_name`: 服务名称
- `lines`: 要获取的日志行数（默认100）

### list_services
列出所有可用的服务

### test_ssh_connection
测试SSH连接

参数：
- `hostname`: 服务器主机名或IP地址
- `username`: SSH用户名
- `password`: SSH密码

## 使用方式

### 作为MCP服务运行

```bash
python main.py
```

### 作为客户端使用

```python
from ssh_mcp_client import ssh_mcp_client

# 获取服务列表
services = await ssh_mcp_client.list_services()

# 获取服务状态
status = await ssh_mcp_client.get_service_status("dify")

# 执行服务操作
result = await ssh_mcp_client.execute_service_operation("dify", "restart")

# 执行远程命令
result = await ssh_mcp_client.execute_remote_command(
    "***********", "ubuntu", "password", "ls -la"
)
```

## 注意事项

1. 密码以明文形式存储在配置文件中，请确保文件权限安全
2. SSH连接会被缓存以提高效率，但需要注意连接超时问题
3. Supervisor命令路径可以在服务配置中自定义
4. 支持Docker Compose和Supervisor两种服务管理方式 