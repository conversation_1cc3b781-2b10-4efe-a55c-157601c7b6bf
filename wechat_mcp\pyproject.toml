[project]
name = "wechat-mcp-server"
version = "1.0.0"
description = "企业微信机器人MCP服务器，提供企业微信通知功能的MCP工具"
readme = "README.md"
requires-python = ">=3.11"
authors = [
    {name = "AI运维代理", email = "<EMAIL>"}
]
license = {file = "LICENSE"}
keywords = ["mcp", "wechat", "notification", "ai", "bot"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Communications :: Chat",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: System :: Monitoring",
]
dependencies = [
    "httpx>=0.28.1",
    "loguru>=0.7.3",
    "mcp>=1.9.3",
    "pydantic>=2.11.5",
]

[project.urls]
Homepage = "https://github.com/your-org/wechat-mcp-server"
Repository = "https://github.com/your-org/wechat-mcp-server.git"
Documentation = "https://github.com/your-org/wechat-mcp-server#readme"
Issues = "https://github.com/your-org/wechat-mcp-server/issues"

[project.scripts]
wechat-mcp-server = "main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
]

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.hatch.build.targets.wheel]
packages = ["."]
