#!/usr/bin/env python3
"""
完整工作流程测试
模拟监控模块发现OCR服务异常，并启动AI自主诊断的完整流程
"""

import asyncio
import sys
import json
import time
import random
from datetime import datetime
from loguru import logger

# 添加当前目录到路径
sys.path.append('.')

from diagnostic_agent import DiagnosticAgent
from monitor_service import MonitorService
from ssh_mcp_client import ssh_mcp_client
# 暂时注释掉微信MCP的导入
# from wechat_mcp_client import wechat_mcp_client

class MockOCRService:
    """模拟OCR服务，用于触发故障"""
    
    def __init__(self):
        self.is_running = True
        self.failure_types = [
            "port_conflict",      # 端口冲突
            "process_crash",      # 进程崩溃
            "config_error",       # 配置错误
            "dependency_fail",    # 依赖失败
            "resource_exhausted"  # 资源耗尽
        ]
        self.current_failure = None
    
    def trigger_random_failure(self):
        """触发随机故障"""
        self.current_failure = random.choice(self.failure_types)
        self.is_running = False
        
        failure_messages = {
            "port_conflict": "端口8080已被占用，服务无法启动",
            "process_crash": "OCR进程意外崩溃，退出代码1",
            "config_error": "配置文件读取失败，参数错误",
            "dependency_fail": "数据库连接失败，无法访问必要服务",
            "resource_exhausted": "内存不足，服务无法正常运行"
        }
        
        return failure_messages.get(self.current_failure, "未知错误")
    
    def simulate_service_check(self):
        """模拟服务健康检查"""
        if not self.is_running:
            raise Exception(f"OCR服务故障: {self.current_failure}")
        return {"status": "healthy", "response_time": "150ms"}

class CompleteWorkflowTest:
    """完整工作流程测试类"""
    
    def __init__(self):
        self.diagnostic_agent = DiagnosticAgent()
        self.monitor_service = MonitorService(diagnostic_agent=self.diagnostic_agent)
        self.mock_ocr = MockOCRService()
        self.test_results = []
        
    async def setup_test_environment(self):
        """设置测试环境"""
        logger.info("🔧 设置测试环境...")
        
        # 检查MCP服务可用性
        ssh_available = await self._check_ssh_mcp_availability()
        # 暂时跳过微信MCP检查
        wechat_available = True  # await self._check_wechat_mcp_availability()
        
        logger.info(f"SSH MCP可用性: {'✅' if ssh_available else '❌'}")
        logger.info(f"微信MCP可用性: {'🚫 已禁用' if True else '❌'}")
        
        # 暂时不发送微信通知
        # if wechat_available:
        #     await wechat_mcp_client.send_system_alert(
        #         "info", "test_system", "localhost",
        #         "🧪 开始进行完整工作流程测试", "low"
        #     )
        
        # 只要任何一个服务可用就继续测试
        return ssh_available or wechat_available
    
    async def _check_ssh_mcp_availability(self):
        """检查SSH MCP可用性"""
        try:
            result = await ssh_mcp_client.list_services()
            # 改进错误处理：即使返回错误也认为服务可用（只要能连接到MCP）
            if isinstance(result, dict) and ("success" in result or "error" in result):
                return True
            return result.get("success", False) if isinstance(result, dict) else False
        except Exception as e:
            logger.error(f"SSH MCP不可用: {e}")
            return False
    
    async def _check_wechat_mcp_availability(self):
        """检查微信MCP可用性（暂时禁用）"""
        # 暂时直接返回False，跳过微信MCP
        logger.info("微信MCP功能已暂时禁用")
        return False
        # try:
        #     result = await wechat_mcp_client.send_text_message("测试连接")
        #     return result
        # except Exception as e:
        #     logger.error(f"微信MCP不可用: {e}")
        #     return False
    
    async def simulate_service_monitoring(self):
        """模拟服务监控流程"""
        logger.info("📊 开始模拟服务监控...")
        
        # 模拟正常运行一段时间
        for i in range(3):
            try:
                status = self.mock_ocr.simulate_service_check()
                logger.info(f"✅ OCR服务健康检查 {i+1}/3: {status}")
                await asyncio.sleep(2)
            except Exception as e:
                logger.error(f"❌ 服务检查失败: {e}")
                return False
        
        # 触发故障
        failure_message = self.mock_ocr.trigger_random_failure()
        logger.warning(f"🚨 触发服务故障: {failure_message}")
        
        # 模拟监控系统检测到故障
        try:
            self.mock_ocr.simulate_service_check()
        except Exception as e:
            logger.error(f"💥 监控系统检测到故障: {str(e)}")
            return str(e)
        
        return failure_message
    
    async def start_ai_diagnosis(self, error_message: str):
        """启动AI自主诊断"""
        logger.info("🤖 启动AI自主诊断流程...")
        
        # 暂时跳过微信通知
        # await wechat_mcp_client.send_diagnosis_update(
        #     "test_session_001",
        #     "ocr_1111_test",
        #     "***********",
        #     "starting",
        #     "检测到OCR服务故障，开始AI自主诊断",
        #     0
        # )
        
        # 启动持续诊断会话
        session_id = await self.diagnostic_agent.start_continuous_diagnosis(
            service_name="ocr_1111_test",
            server_ip="***********",
            error_info=error_message,
            max_attempts=3  # 测试环境减少尝试次数
        )
        
        logger.info(f"🔍 AI诊断会话已启动: {session_id}")
        
        # 等待诊断完成
        await self._wait_for_diagnosis_completion(session_id)
        
        return session_id
    
    async def _wait_for_diagnosis_completion(self, session_id: str, max_wait_time: int = 300):
        """等待诊断完成"""
        logger.info(f"⏱️ 等待诊断会话完成: {session_id}")
        
        start_time = time.time()
        while time.time() - start_time < max_wait_time:
            session_status = self.diagnostic_agent.get_session_status(session_id)
            
            if session_status:
                status = session_status.get("status")
                attempts = session_status.get("current_attempt", 0)
                
                logger.info(f"📈 诊断进度: 状态={status}, 尝试次数={attempts}")
                
                if status in ["resolved", "failed", "exhausted"]:
                    logger.info(f"🏁 诊断会话结束: {status}")
                    break
            
            await asyncio.sleep(10)  # 每10秒检查一次
        else:
            logger.warning("⏰ 诊断等待超时")
    
    async def verify_mcp_tool_usage(self, session_id: str):
        """验证MCP工具使用情况"""
        logger.info("🔍 验证MCP工具使用情况...")
        
        session_status = self.diagnostic_agent.get_session_status(session_id)
        if not session_status:
            logger.error("❌ 无法获取会话状态")
            return False
        
        attempts = session_status.get("attempts", [])
        
        # 检查是否使用了MCP工具
        mcp_tools_used = []
        for attempt in attempts:
            execution_results = attempt.get("execution_results", [])
            for result in execution_results:
                tool_name = result.get("tool", "")
                if "ssh" in tool_name.lower() or "wechat" in tool_name.lower():
                    mcp_tools_used.append(tool_name)
        
        if mcp_tools_used:
            logger.info(f"✅ MCP工具使用验证成功，使用的工具: {set(mcp_tools_used)}")
            return True
        else:
            logger.warning("⚠️ 未检测到MCP工具使用")
            return False
    
    async def generate_test_report(self, session_id: str):
        """生成测试报告"""
        logger.info("📊 生成测试报告...")
        
        session_status = self.diagnostic_agent.get_session_status(session_id)
        if not session_status:
            logger.error("❌ 无法生成报告，会话状态不存在")
            return None
        
        report = {
            "test_id": f"complete_workflow_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "timestamp": datetime.now().isoformat(),
            "service_name": "ocr_1111_test",
            "failure_type": self.mock_ocr.current_failure,
            "session_id": session_id,
            "session_status": session_status,
            "mcp_tools_verification": await self.verify_mcp_tool_usage(session_id),
            "overall_result": session_status.get("status") == "resolved"
        }
        
        # 保存报告到文件
        report_path = f"logs/complete_workflow_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"📄 测试报告已保存: {report_path}")
        
        # 暂时跳过微信通知
        # await wechat_mcp_client.send_markdown_message(f"""
        # # 🧪 完整工作流程测试报告
        # 
        # ## 基本信息
        # - **测试ID**: {report['test_id']}
        # - **服务名称**: {report['service_name']}
        # - **故障类型**: {report['failure_type']}
        # - **会话ID**: {session_id}
        # 
        # ## 测试结果
        # - **整体结果**: {'✅ 成功' if report['overall_result'] else '❌ 失败'}
        # - **最终状态**: {session_status.get('status')}
        # - **尝试次数**: {session_status.get('current_attempt', 0)}
        # - **MCP工具验证**: {'✅ 通过' if report['mcp_tools_verification'] else '❌ 未通过'}
        # 
        # ## 详细信息
        # - **开始时间**: {session_status.get('start_time')}
        # - **结果**: {session_status.get('final_result', '无')}
        # 
        # 报告文件: `{report_path}`
        #         """)
        
        # 在控制台输出报告摘要
        logger.info(f"""
📋 测试报告摘要:
- 测试ID: {report['test_id']}
- 服务名称: {report['service_name']}
- 故障类型: {report['failure_type']}
- 整体结果: {'✅ 成功' if report['overall_result'] else '❌ 失败'}
- 最终状态: {session_status.get('status')}
- MCP工具验证: {'✅ 通过' if report['mcp_tools_verification'] else '❌ 未通过'}
        """)
        
        return report
    
    async def run_complete_test(self):
        """运行完整测试流程"""
        logger.info("🚀 开始完整工作流程测试")
        
        try:
            # 1. 设置测试环境
            env_ready = await self.setup_test_environment()
            if not env_ready:
                logger.warning("⚠️ 测试环境准备不完整，但继续进行测试")
            
            # 2. 模拟服务监控
            logger.info("\n" + "="*50)
            logger.info("阶段1: 服务监控模拟")
            logger.info("="*50)
            
            failure_message = await self.simulate_service_monitoring()
            if not failure_message:
                logger.error("❌ 服务监控模拟失败")
                return False
            
            # 3. 启动AI诊断
            logger.info("\n" + "="*50)
            logger.info("阶段2: AI自主诊断")
            logger.info("="*50)
            
            session_id = await self.start_ai_diagnosis(failure_message)
            
            # 4. 生成测试报告
            logger.info("\n" + "="*50)
            logger.info("阶段3: 测试报告生成")
            logger.info("="*50)
            
            report = await self.generate_test_report(session_id)
            
            # 5. 输出测试结果
            logger.info("\n" + "="*50)
            logger.info("🏁 测试完成总结")
            logger.info("="*50)
            
            if report and report.get("overall_result"):
                logger.info("✅ 完整工作流程测试成功！")
                logger.info(f"✅ AI成功自主诊断并解决了 {self.mock_ocr.current_failure} 类型的故障")
                logger.info(f"✅ MCP工具调用验证: {'通过' if report.get('mcp_tools_verification') else '未通过'}")
            else:
                logger.warning("⚠️ 测试完成但结果不理想")
                logger.warning(f"⚠️ 故障类型: {self.mock_ocr.current_failure}")
                logger.warning(f"⚠️ 最终状态: {report.get('session_status', {}).get('status') if report else '未知'}")
            
            return True
            
        except Exception as e:
            logger.error(f"💥 测试过程中发生错误: {e}")
            # 暂时跳过微信错误通知
            # try:
            #     await wechat_mcp_client.send_system_alert(
            #         "error", "test_system", "localhost",
            #         f"完整工作流程测试失败: {str(e)}", "high"
            #     )
            # except:
            #     pass
            return False

async def main():
    """主函数"""
    logger.info("🧪 完整工作流程测试启动")
    
    # 创建测试实例
    test = CompleteWorkflowTest()
    
    # 运行测试
    success = await test.run_complete_test()
    
    if success:
        logger.info("🎉 所有测试流程执行完成")
        return 0
    else:
        logger.error("💥 测试流程执行失败")
        return 1

if __name__ == "__main__":
    # 配置日志格式
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # 运行测试
    exit_code = asyncio.run(main()) 