{"test_type": "failure_scenarios", "summary": {"total_tests": 7, "passed_tests": 4, "failed_tests": 3, "success_rate": 57.14285714285714}, "results": [{"test_name": "API连通性", "success": true, "message": "API服务响应正常", "timestamp": "2025-06-18T15:49:22.468752"}, {"test_name": "监控状态获取", "success": true, "message": "监控中的服务数: 1", "timestamp": "2025-06-18T15:49:23.481030"}, {"test_name": "微信通知测试", "success": false, "message": "通知发送失败", "timestamp": "2025-06-18T15:49:27.758654"}, {"test_name": "手动诊断触发", "success": true, "message": "诊断会话已启动: nginx_localhost_20250618_154928", "timestamp": "2025-06-18T15:49:28.808101"}, {"test_name": "诊断会话完成", "success": false, "message": "最终状态: exhausted, 结果: 无结果信息", "timestamp": "2025-06-18T15:51:06.020029"}, {"test_name": "手动诊断触发", "success": true, "message": "诊断会话已启动: docker-service_localhost_20250618_155117", "timestamp": "2025-06-18T15:51:17.060389"}, {"test_name": "诊断会话完成", "success": false, "message": "最终状态: exhausted, 结果: 无结果信息", "timestamp": "2025-06-18T15:52:54.318410"}]}