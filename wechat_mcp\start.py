#!/usr/bin/env python3
"""
企业微信MCP服务器快速启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path


def check_uv_installed():
    """检查uv是否已安装"""
    try:
        subprocess.run(["uv", "--version"], check=True, capture_output=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False


def check_environment():
    """检查环境配置"""
    webhook_url = os.getenv("WECHAT_WEBHOOK_URL")
    if not webhook_url:
        print("❌ 环境变量 WECHAT_WEBHOOK_URL 未设置")
        print("💡 请先设置企业微信Webhook URL:")
        print("   export WECHAT_WEBHOOK_URL='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY'")
        print("\n📖 或者创建 .env 文件:")
        print("   WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY")
        print("   WECHAT_ENABLED=true")
        return False
    return True


def load_env_file():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        print("📁 发现.env文件，正在加载...")
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    key_clean = key.strip().lstrip('\ufeff')  # 移除BOM字符
                    os.environ[key_clean] = value.strip()
        print("✅ .env文件加载完成")


def main():
    """主函数"""
    print("🚀 企业微信MCP服务器启动器")
    print("=" * 40)
    
    # 检查uv是否安装
    if not check_uv_installed():
        print("❌ uv 未安装或不在PATH中")
        print("💡 请先安装uv: https://docs.astral.sh/uv/getting-started/installation/")
        sys.exit(1)
    
    print("✅ uv 已安装")
    
    # 加载.env文件
    load_env_file()
    
    # 检查环境配置
    if not check_environment():
        sys.exit(1)
    
    print("✅ 环境配置检查通过")
    print("\n🔄 启动MCP服务器...")
    
    try:
        # 启动MCP服务器
        subprocess.run(["uv", "run", "python", "main.py"], check=True)
    except KeyboardInterrupt:
        print("\n\n👋 MCP服务器已停止")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 