# 企业微信机器人 MCP 服务器

本项目将企业微信机器人功能封装为 MCP (Model Context Protocol) 服务，允许大模型通过工具调用的方式发送企业微信通知。

## 功能特性

- 🤖 **MCP 标准化接口**: 完全兼容 MCP 协议，可与任何支持 MCP 的 AI 客户端集成
- 📱 **企业微信通知**: 支持发送文本和 Markdown 格式消息到企业微信群
- 🚨 **智能警报**: 大模型可根据参数自动生成格式化的系统警报通知
- 🔄 **诊断更新**: 支持实时发送诊断进度更新
- ⚙️ **灵活配置**: 通过环境变量配置 Webhook URL 和开关状态

## 安装和配置

### 1. 环境要求

- Python 3.11+
- uv 包管理器

### 2. 安装依赖

```bash
cd wechat_mcp
uv sync
```

### 3. 环境变量配置

复制 `env.example` 为 `.env` 并配置：

```bash
# 企业微信机器人Webhook URL
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_BOT_KEY

# 是否启用企业微信通知
WECHAT_ENABLED=true
```

### 4. 获取企业微信 Webhook URL

1. 打开企业微信群聊
2. 点击群设置 → 群机器人 → 添加机器人
3. 创建机器人后复制 Webhook URL
4. 将 URL 设置到环境变量 `WECHAT_WEBHOOK_URL`

## MCP 集成配置

### Claude Desktop 配置

在 `%APPDATA%\Claude\claude_desktop_config.json` 中添加：

```json
{
  "mcpServers": {
    "wechat-mcp-server": {
      "command": "uv",
      "args": ["run", "python", "main.py"],
      "cwd": "D:/path/to/wechat_mcp",
      "env": {
        "WECHAT_WEBHOOK_URL": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_BOT_KEY",
        "WECHAT_ENABLED": "true"
      }
    }
  }
}
```

### 其他 MCP 客户端

参考 `config.json.example` 文件进行配置。

## 可用工具

### 1. send_text_message
发送简单文本消息到企业微信群。

**参数:**
- `content` (string, 必需): 文本消息内容，最长2048字节
- `mentioned_list` (array, 可选): 提醒人员列表，使用"@all"提醒所有人

### 2. send_markdown_message
发送 Markdown 格式消息到企业微信群。

**参数:**
- `content` (string, 必需): Markdown 格式的消息内容

### 3. send_system_alert
发送系统警报通知，大模型会根据参数自动生成格式化消息。

**参数:**
- `alert_type` (enum, 必需): 警报类型 - "error", "warning", "info", "success"
- `service_name` (string, 必需): 服务名称
- `server_info` (string, 必需): 服务器信息（IP或主机名）
- `description` (string, 必需): 问题描述或详细信息
- `severity` (enum, 可选): 严重程度 - "low", "medium", "high", "critical"，默认 "medium"

### 4. send_diagnosis_update
发送诊断进度更新通知。

**参数:**
- `session_id` (string, 必需): 诊断会话ID
- `service_name` (string, 必需): 服务名称
- `server_info` (string, 必需): 服务器信息
- `status` (enum, 必需): 诊断状态 - "started", "in_progress", "completed", "failed"
- `message` (string, 必需): 状态描述或结果信息
- `attempts` (integer, 可选): 尝试次数，默认 1

## 使用示例

### 在 Claude 中使用

配置完成后，你可以直接对 Claude 说：

```
"请发送一个系统警报，服务是 nginx，服务器是 *************，出现了连接超时问题，严重程度是高"
```

Claude 会自动调用 `send_system_alert` 工具，生成格式化的警报消息并发送到企业微信群。

### 手动测试

```bash
# 启动 MCP 服务器
cd wechat_mcp
uv run python main.py
```

## 开发和扩展

### 项目结构

```
wechat_mcp/
├── main.py              # MCP 服务器主文件
├── pyproject.toml       # uv 项目配置
├── uv.lock             # 依赖锁定文件
├── config.json.example  # MCP 配置示例
├── env.example         # 环境变量示例
└── README.md           # 本文档
```

### 添加新的工具

1. 在 `handle_list_tools()` 函数中添加新的 Tool 定义
2. 在 `handle_call_tool()` 函数中添加对应的处理逻辑
3. 根据需要扩展 `WechatBotMCP` 类的功能

### 日志配置

项目使用 loguru 进行日志记录，可以通过环境变量调整日志级别：

```bash
export LOGURU_LEVEL=DEBUG
```

## 故障排除

### 常见问题

1. **连接失败**: 检查 Webhook URL 是否正确，网络是否通畅
2. **权限错误**: 确保机器人在对应的企业微信群中
3. **消息格式错误**: 检查 Markdown 格式是否正确
4. **环境变量未加载**: 确保正确设置了环境变量

### 调试模式

启动时设置调试级别：

```bash
LOGURU_LEVEL=DEBUG uv run python main.py
```

## 许可证

本项目基于原有的企业微信通知模块进行 MCP 封装，保持相同的许可证条款。
