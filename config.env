# AI运维代理环境配置
# Environment Configuration for AI Operations Agent

#============================================
# 服务器配置 / Server Configuration
#============================================
SERVER_HOST=0.0.0.0
SERVER_PORT=8000

#============================================
# AI模型配置 / AI Model Configuration  
#============================================
# 本地AI模型服务地址 (vLLM服务器)
LOCAL_MODEL_BASE_URL=http://************:8288/v1
LOCAL_MODEL_NAME=qwq-32b
LOCAL_MODEL_API_KEY=weshare_llm

# AI模型请求配置
MODEL_MAX_TOKENS=4000
MODEL_TEMPERATURE=0.3

#============================================
# 安全分级配置 / Security Level Configuration
#============================================
# 安全级别: high=高安全(限制AI只能选择预设方案), low=低安全(AI完全自主决定)
# Security Level: high=High Security(AI limited to predefined strategies), low=Low Security(AI full autonomy)
SECURITY_LEVEL=low

# 预设策略模式开关 (仅当SECURITY_LEVEL=high时生效)
# Predefined Strategy Mode (Only effective when SECURITY_LEVEL=high)
ENABLE_PREDEFINED_STRATEGIES=true

#============================================
# SSH连接配置 / SSH Connection Configuration
#============================================
# SSH连接超时设置(秒)
SSH_CONNECT_TIMEOUT=30
SSH_COMMAND_TIMEOUT=60

# SSH重试配置
SSH_MAX_RETRIES=3
SSH_RETRY_DELAY=2

#============================================
# 诊断配置 / Diagnosis Configuration
#============================================
# 最大诊断尝试次数
MAX_DIAGNOSIS_ATTEMPTS=5

# 诊断日志保留天数
DIAGNOSIS_LOG_RETENTION_DAYS=30

# 是否启用详细诊断日志
ENABLE_DETAILED_DIAGNOSIS_LOG=true

#============================================
# 系统配置 / System Configuration
#============================================
# 日志级别: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO

# 是否启用调试模式
DEBUG_MODE=false

# 工作目录
WORK_DIR=/tmp/ops_agent

#============================================
# 微信MCP配置 / WeChat MCP Configuration  
#============================================
# 微信MCP服务开关
ENABLE_WECHAT_MCP=true

# MCP服务路径
WECHAT_MCP_PATH=wechat_mcp

# 向后兼容的微信机器人配置
ENABLE_WECHAT_BOT=false
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=099f175d-8aff-49e9-805c-160592ccb213
WECHAT_BOT_SECRET=

#============================================
# 性能配置 / Performance Configuration
#============================================
# 并发诊断任务数量限制
MAX_CONCURRENT_DIAGNOSES=3

# API请求频率限制(每分钟)
API_RATE_LIMIT_PER_MINUTE=60

# 内存使用限制(MB)
MEMORY_LIMIT_MB=512 