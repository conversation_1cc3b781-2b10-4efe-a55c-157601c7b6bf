import logging
import requests
import time
from threading import Thread
from typing import Dict, List, Optional
import json
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MonitorService:
    """服务监控类"""
    
    def __init__(self, diagnostic_agent=None):
        self.services: Dict[str, Dict] = {}  # 服务配置字典
        self.monitor_threads: Dict[str, Thread] = {}  # 监控线程字典
        self.stop_flag = False
        self.diagnostic_agent = diagnostic_agent
        self.wechat_webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=772cb49d-aaa9"
        
    def load_services_from_config(self, config_file: str = "service_url_mapping.json"):
        """从配置文件加载服务映射"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            for service in config.get('services', []):
                self.add_service(
                    service_name=service['service_name'],
                    url=service['url'],
                    server_ip=service['server_ip']
                )
            logger.info(f"从配置文件加载了 {len(config.get('services', []))} 个服务")
            return True
        except Exception as e:
            logger.error(f"加载服务配置失败: {e}")
            return False
        
    def add_service(self, service_name: str, url: str, server_ip: str):
        """添加服务到监控列表"""
        self.services[service_name] = {
            "url": url,
            "server_ip": server_ip,
            "status": True,
            "last_check": None,
            "error_count": 0
        }
        logger.info(f"添加服务到监控: {service_name} ({url})")
        
    def remove_service(self, service_name: str):
        """从监控列表移除服务"""
        if service_name in self.services:
            del self.services[service_name]
            logger.info(f"从监控移除服务: {service_name}")
            
    def start_monitoring(self):
        """启动所有服务的监控"""
        self.stop_flag = False
        for service_name in self.services:
            if service_name not in self.monitor_threads:
                thread = Thread(target=self._monitor_service, args=(service_name,))
                self.monitor_threads[service_name] = thread
                thread.start()
                logger.info(f"启动服务监控线程: {service_name}")
                
    def stop_monitoring(self):
        """停止所有服务的监控"""
        self.stop_flag = True
        for thread in self.monitor_threads.values():
            thread.join()
        self.monitor_threads.clear()
        logger.info("所有监控线程已停止")
        
    def _monitor_service(self, service_name: str):
        """监控单个服务的线程函数"""
        service = self.services[service_name]
        test_data = {
            "image": "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",
            "target": ["中华人民共和国", "居民身份证", "性别"],
            "asyntrans": True,
            "id": 123,
            "url": "http://***********:8190/rms/ocr/resultSync"
        }
        
        while not self.stop_flag:
            try:
                logger.info(f'检查服务健康状态: {service_name}')
                response = requests.post(service['url'], json=test_data, timeout=180)
                
                if response.status_code == 200:
                    service['status'] = True
                    service['error_count'] = 0
                else:
                    service['status'] = False
                    service['error_count'] += 1
                    self._handle_service_error(service_name, service)
                    
            except Exception as e:
                service['status'] = False
                service['error_count'] += 1
                self._handle_service_error(service_name, service)
                
            service['last_check'] = datetime.now()
            time.sleep(180)  # 每3分钟检查一次
            
    def _handle_service_error(self, service_name: str, service: Dict):
        """处理服务错误"""
        error_message = f"服务 {service_name} ({service['url']}) 出现异常！"
        logger.warning(error_message)
        
        # 发送企业微信通知
        self._send_wechat_alert(error_message)
        
        # 如果启用了诊断代理，启动诊断
        if self.diagnostic_agent:
            try:
                session_id = self.diagnostic_agent.start_continuous_diagnosis(
                    service_name=service_name,
                    server_ip=service['server_ip'],
                    error_info=error_message,
                    max_attempts=10
                )
                logger.info(f"已启动诊断会话: {session_id}")
            except Exception as e:
                logger.error(f"启动诊断失败: {e}")
                
    def _send_wechat_alert(self, message: str):
        """发送企业微信告警"""
        try:
            headers = {'Content-Type': 'application/json'}
            data = {
                "msgtype": "text",
                "text": {
                    "content": message,
                    "mentioned_mobile_list": ["@聂宗强"]
                }
            }
            response = requests.post(self.wechat_webhook_url, headers=headers, json=data)
            return response.json()
        except Exception as e:
            logger.error(f"发送企业微信告警失败: {e}")
            return None
            
    def get_service_status(self, service_name: str) -> Optional[Dict]:
        """获取服务状态"""
        if service_name in self.services:
            return {
                "service_name": service_name,
                "url": self.services[service_name]["url"],
                "status": self.services[service_name]["status"],
                "last_check": self.services[service_name]["last_check"],
                "error_count": self.services[service_name]["error_count"]
            }
        return None
        
    def get_all_services_status(self) -> List[Dict]:
        """获取所有服务状态"""
        return [
            {
                "service_name": name,
                "url": service["url"],
                "status": service["status"],
                "last_check": service["last_check"],
                "error_count": service["error_count"]
            }
            for name, service in self.services.items()
        ] 