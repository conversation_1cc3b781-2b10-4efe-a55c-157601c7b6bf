#!/usr/bin/env python3
"""
SSH MCP客户端
用于与ssh_mcp服务通信，提供SSH操作和服务管理功能
"""

import json
import subprocess
import asyncio
from typing import Optional, Dict, Any, List
from loguru import logger
from pathlib import Path
import os


class SSHMCPClient:
    """SSH MCP客户端"""
    
    def __init__(self, mcp_path: str = "ssh_mcp"):
        # 使用绝对路径，防止后续 cwd 变化导致路径丢失
        self.mcp_path = Path(mcp_path).resolve()
        self.enabled = self._check_mcp_available()
        
    def _check_mcp_available(self) -> bool:
        """检查MCP服务是否可用"""
        try:
            if not self.mcp_path.exists():
                logger.warning(f"SSH MCP路径不存在: {self.mcp_path}")
                return False
            
            # 检查main.py是否存在
            main_py = self.mcp_path / "main.py"
            if not main_py.exists():
                logger.warning(f"SSH MCP主文件不存在: {main_py}")
                return False
            
            logger.info("SSH MCP服务可用")
            return True
            
        except Exception as e:
            logger.warning(f"SSH MCP检查失败: {e}")
            return False
    
    async def execute_remote_command(self, hostname: str, username: str, password: str,
                                   command: str, use_sudo: bool = False, timeout: int = 30) -> Dict[str, Any]:
        """执行远程命令"""
        if not self.enabled:
            logger.warning("SSH MCP服务不可用，跳过命令执行")
            return {"success": False, "error": "SSH MCP服务不可用"}
        
        try:
            tool_call = {
                "name": "execute_remote_command",
                "arguments": {
                    "hostname": hostname,
                    "username": username,
                    "password": password,
                    "command": command,
                    "use_sudo": use_sudo,
                    "timeout": timeout
                }
            }
            
            result = await self._call_mcp_tool(tool_call)
            return json.loads(result) if isinstance(result, str) else result
            
        except Exception as e:
            logger.error(f"执行远程命令失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def execute_service_operation(self, service_name: str, operation: str, 
                                      log_lines: int = 100) -> Dict[str, Any]:
        """执行服务操作"""
        if not self.enabled:
            logger.warning("SSH MCP服务不可用，跳过服务操作")
            return {"success": False, "error": "SSH MCP服务不可用"}
        
        try:
            tool_call = {
                "name": "execute_service_operation",
                "arguments": {
                    "service_name": service_name,
                    "operation": operation,
                    "log_lines": log_lines
                }
            }
            
            result = await self._call_mcp_tool(tool_call)
            return json.loads(result) if isinstance(result, str) else result
            
        except Exception as e:
            logger.error(f"执行服务操作失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_service_status(self, service_name: str) -> Dict[str, Any]:
        """获取服务状态"""
        if not self.enabled:
            logger.warning("SSH MCP服务不可用，跳过获取服务状态")
            return {"success": False, "error": "SSH MCP服务不可用"}
        
        try:
            tool_call = {
                "name": "get_service_status",
                "arguments": {
                    "service_name": service_name
                }
            }
            
            result = await self._call_mcp_tool(tool_call)
            return json.loads(result) if isinstance(result, str) else result
            
        except Exception as e:
            logger.error(f"获取服务状态失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_service_logs(self, service_name: str, lines: int = 100) -> Dict[str, Any]:
        """获取服务日志"""
        if not self.enabled:
            logger.warning("SSH MCP服务不可用，跳过获取服务日志")
            return {"success": False, "error": "SSH MCP服务不可用"}
        
        try:
            tool_call = {
                "name": "get_service_logs",
                "arguments": {
                    "service_name": service_name,
                    "lines": lines
                }
            }
            
            result = await self._call_mcp_tool(tool_call)
            return json.loads(result) if isinstance(result, str) else result
            
        except Exception as e:
            logger.error(f"获取服务日志失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def list_services(self) -> Dict[str, Any]:
        """列出所有可用的服务"""
        if not self.enabled:
            logger.warning("SSH MCP服务不可用，跳过列出服务")
            return {"success": False, "error": "SSH MCP服务不可用"}
        
        try:
            tool_call = {
                "name": "list_services",
                "arguments": {}
            }
            
            result = await self._call_mcp_tool(tool_call)
            
            # 尝试解析JSON结果
            try:
                if isinstance(result, str):
                    parsed_result = json.loads(result)
                else:
                    parsed_result = result
                
                # 验证结果格式
                if isinstance(parsed_result, dict):
                    return parsed_result
                else:
                    logger.warning(f"list_services返回了意外的数据类型: {type(parsed_result)}")
                    return {"success": False, "error": f"意外的返回数据类型: {type(parsed_result)}"}
                    
            except json.JSONDecodeError as e:
                logger.error(f"无法解析list_services的JSON响应: {e}")
                logger.debug(f"原始响应内容: {result}")
                # 如果JSON解析失败，但结果中包含有用信息，尝试提取
                if isinstance(result, str) and len(result.strip()) > 0:
                    return {"success": False, "error": "JSON解析失败", "raw_response": result}
                else:
                    return {"success": False, "error": f"JSON解析失败: {e}"}
            
        except Exception as e:
            logger.error(f"列出服务失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_ssh_connection(self, hostname: str, username: str, password: str) -> Dict[str, Any]:
        """测试SSH连接"""
        if not self.enabled:
            logger.warning("SSH MCP服务不可用，跳过SSH连接测试")
            return {"success": False, "error": "SSH MCP服务不可用"}
        
        try:
            tool_call = {
                "name": "test_ssh_connection",
                "arguments": {
                    "hostname": hostname,
                    "username": username,
                    "password": password
                }
            }
            
            result = await self._call_mcp_tool(tool_call)
            return json.loads(result) if isinstance(result, str) else result
            
        except Exception as e:
            logger.error(f"测试SSH连接失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _call_mcp_tool(self, tool_call: Dict[str, Any]) -> str:
        """调用MCP工具"""
        original_cwd = os.getcwd()
        try:
            # 切换到MCP目录
            os.chdir(self.mcp_path)
            
            # 构造调用命令（使用当前Python环境）
            import sys
            python_executable = sys.executable
            cmd = [python_executable, "main.py"]
            
            # 构造完整的MCP会话
            # 1. 初始化请求
            init_request = json.dumps({
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "clientInfo": {
                        "name": "ssh-mcp-client",
                        "version": "1.0.0"
                    }
                }
            }) + "\n"
            
            # 2. 初始化完成通知
            initialized_notification = json.dumps({
                "jsonrpc": "2.0",
                "method": "notifications/initialized"
            }) + "\n"
            
            # 3. 工具调用请求
            tool_request = json.dumps({
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/call",
                "params": {
                    "name": tool_call["name"],
                    "arguments": tool_call["arguments"]
                }
            }) + "\n"
            
            # 组合所有输入
            input_data = init_request + initialized_notification + tool_request
            
            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate(input=input_data.encode('utf-8'))
            try:
                stdout = stdout.decode('utf-8')
            except UnicodeDecodeError:
                stdout = stdout.decode('gbk', errors='ignore')
            try:
                stderr = stderr.decode('utf-8')
            except UnicodeDecodeError:
                stderr = stderr.decode('gbk', errors='ignore')
            
            # 恢复原目录
            os.chdir(original_cwd)
            
            if process.returncode == 0:
                logger.info(f"MCP工具调用成功: {tool_call['name']}")
                # 解析MCP响应 - 查找工具调用的响应
                lines = stdout.strip().split('\n')
                for line in lines:
                    # 清理行内容：去除首尾空白和回车符
                    clean_line = line.strip().rstrip('\r\n')
                    if not clean_line:
                        continue
                    
                    try:
                        response = json.loads(clean_line)
                        # 查找ID为2的响应（工具调用响应）
                        if response.get("id") == 2:
                            if "result" in response:
                                mcp_result = response['result']
                                
                                # 检查是否有错误标志
                                if mcp_result.get("isError", False):
                                    # 从错误信息中提取有用内容
                                    error_content = mcp_result.get('content', [])
                                    if error_content and error_content[0].get('text'):
                                        error_text = error_content[0]['text']
                                        logger.warning(f"SSH MCP内部错误: {error_text[:200]}...")
                                        # 尽管有错误，但仍尝试返回一个基本的成功响应
                                        return json.dumps({"success": False, "error": "SSH MCP内部验证错误", "details": error_text[:500]})
                                
                                # 正常响应处理
                                if mcp_result and 'content' in mcp_result and mcp_result['content']:
                                    # 结果在第一个content block的text字段中，是一个json字符串
                                    text_content = mcp_result['content'][0].get('text')
                                    if text_content:
                                        return text_content
                                
                                # 如果格式不正确，记录错误并返回
                                logger.error(f"MCP工具调用成功但响应格式不正确: {mcp_result}")
                                return json.dumps({"success": False, "error": "MCP响应格式不正确"})

                            elif "error" in response:
                                logger.error(f"MCP工具调用错误: {response['error']}")
                                return json.dumps({"success": False, "error": response['error']})
                    except json.JSONDecodeError as e:
                        logger.warning(f"无法解析MCP的输出行: {clean_line[:100]}... (错误: {e})")
                        # 对于无法解析的行，检查是否包含错误信息
                        if "error" in clean_line.lower() or "exception" in clean_line.lower():
                            return json.dumps({"success": False, "error": f"MCP输出包含错误: {clean_line}"})
                        continue
                
                # 如果没有找到标准的JSON RPC响应，尝试解析整个输出
                logger.warning("在MCP输出中未找到标准JSON-RPC响应，尝试解析完整输出")
                if stdout.strip():
                    # 检查输出是否包含明显的错误信息
                    if any(keyword in stdout.lower() for keyword in ["error", "exception", "failed", "不存在", "无法"]):
                        return json.dumps({"success": False, "error": f"MCP执行可能存在问题: {stdout[:200]}..."})
                    else:
                        # 如果看起来正常，返回一个基本的成功响应
                        return json.dumps({"success": True, "message": "MCP调用完成", "output": stdout[:200] + "..." if len(stdout) > 200 else stdout})
                else:
                    logger.warning("MCP输出为空")
                    return json.dumps({"success": False, "error": "MCP输出为空"})
            else:
                logger.error(f"MCP工具调用失败 (返回码: {process.returncode}): {stderr}")
                return json.dumps({"success": False, "error": f"MCP调用失败: {stderr}"})

        except Exception as e:
            logger.error(f"MCP工具调用异常: {e}")
            # 确保恢复原目录
            try:
                os.chdir(original_cwd)
            except:
                pass
            return json.dumps({"success": False, "error": str(e)})
    
    async def test_connection(self) -> bool:
        """测试MCP连接"""
        # 此功能需要服务信息，暂时不实现
        logger.warning("test_connection在客户端尚未完全实现")
        return False


# 全局MCP客户端实例
ssh_mcp_client = SSHMCPClient()

# --- 向后兼容的函数 ---

async def execute_service_operation(service_name: str, operation: str, log_lines: int = 100) -> Dict[str, Any]:
    return await ssh_mcp_client.execute_service_operation(service_name, operation, log_lines)

async def get_service_status(service_name: str) -> Dict[str, Any]:
    return await ssh_mcp_client.get_service_status(service_name)

async def get_service_logs(service_name: str, lines: int = 100) -> Dict[str, Any]:
    return await ssh_mcp_client.get_service_logs(service_name, lines)

async def execute_remote_command(hostname: str, username: str, password: str,
                               command: str, use_sudo: bool = False, timeout: int = 30) -> Dict[str, Any]:
    return await ssh_mcp_client.execute_remote_command(hostname, username, password, command, use_sudo, timeout)


# --- 测试代码 ---
async def test():
    """测试客户端功能"""
    logger.info("--- 开始SSH MCP客户端测试 ---")
    
    # 在实际使用中，服务信息应该从一个可靠的来源获取
    # 这里为了测试，我们假设 service_info.txt 中存在名为'my-test-service'的服务
    test_service = "my-test-service" 
    
    # 1. 测试列出服务
    logger.info("\n--- 1. 测试 list_services ---")
    services_result = await ssh_mcp_client.list_services()
    if services_result.get("success"):
        logger.info(f"成功列出服务: {services_result.get('services')}")
    else:
        logger.error(f"列出服务失败: {services_result.get('error')}")

    # 2. 测试获取服务状态
    logger.info(f"\n--- 2. 测试 get_service_status for {test_service} ---")
    status_result = await get_service_status(test_service)
    if status_result.get("success"):
        logger.info(f"服务状态: {status_result.get('status')}")
    else:
        logger.error(f"获取服务状态失败: {status_result.get('error')}")
        
    # 3. 测试重启服务
    logger.info(f"\n--- 3. 测试 execute_service_operation (restart) for {test_service} ---")
    restart_result = await execute_service_operation(test_service, "restart")
    if restart_result.get("success"):
        logger.info(f"服务重启成功: {restart_result.get('stdout')}")
    else:
        logger.error(f"服务重启失败: {restart_result.get('error')}")

    # 4. 测试获取服务日志
    logger.info(f"\n--- 4. 测试 get_service_logs for {test_service} ---")
    logs_result = await get_service_logs(test_service, lines=20)
    if logs_result.get("success"):
        logger.info(f"获取到服务日志:\n{logs_result.get('logs')}")
    else:
        logger.error(f"获取服务日志失败: {logs_result.get('error')}")

    logger.info("\n--- SSH MCP客户端测试结束 ---")


if __name__ == "__main__":
    # 配置loguru
    log_path = Path(__file__).parent / "ssh_mcp_client.log"
    logger.add(log_path, rotation="10 MB", retention="7 days", level="DEBUG")
    
    # 运行测试
    asyncio.run(test()) 