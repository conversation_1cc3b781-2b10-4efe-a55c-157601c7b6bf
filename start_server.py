#!/usr/bin/env python3
"""
AI运维代理服务器启动脚本
用于启动API服务器并进行基本功能验证
"""

import asyncio
import sys
import signal
import time
from pathlib import Path
from loguru import logger
import subprocess
import threading

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def setup_logging():
    """设置日志配置"""
    logger.remove()  # 移除默认处理器
    
    # 添加控制台日志
    logger.add(
        sys.stdout,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
        colorize=True
    )

def check_dependencies():
    """检查依赖是否已安装"""
    logger.info("🔍 检查依赖包...")
    
    try:
        import uvicorn
        import fastapi
        import loguru
        import pydantic
        logger.info("✅ 所有依赖包已安装")
        return True
    except ImportError as e:
        logger.error(f"❌ 缺少依赖包: {e}")
        logger.info("请运行: pip install -r requirements.txt")
        return False

def start_api_server():
    """启动API服务器 (在子进程中运行，避免signal冲突)"""
    logger.info("🚀 启动AI运维代理API服务器...")

    try:
        python_executable = sys.executable
        # 使用uvicorn直接启动 FastAPI 应用，确保运行在独立进程，且不会与当前事件循环/信号冲突
        app_path = "main:app"
        process = subprocess.Popen([
            python_executable, "-m", "uvicorn", app_path,
            "--host", "0.0.0.0", "--port", "8000", "--log-level", "info"
        ])

        logger.info(f"✅ API服务器子进程 (PID={process.pid}) 已启动")
        return process

    except Exception as e:
        logger.error(f"❌ API服务器启动失败: {e}")
        return None

async def test_api_endpoints():
    """测试API端点"""
    logger.info("🧪 测试API端点...")
    
    # 等待服务器启动
    await asyncio.sleep(3)
    
    try:
        import httpx
        
        base_url = "http://localhost:8000"
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            # 测试根端点
            try:
                response = await client.get(f"{base_url}/")
                if response.status_code == 200:
                    logger.info("✅ 根端点响应正常")
                else:
                    logger.warning(f"⚠️ 根端点状态码: {response.status_code}")
            except Exception as e:
                logger.error(f"❌ 根端点测试失败: {e}")
            
            # 测试服务列表端点
            try:
                response = await client.get(f"{base_url}/services")
                if response.status_code == 200:
                    services = response.json()
                    logger.info(f"✅ 服务列表端点正常，获取到 {len(services)} 个服务")
                else:
                    logger.warning(f"⚠️ 服务列表端点状态码: {response.status_code}")
            except Exception as e:
                logger.error(f"❌ 服务列表端点测试失败: {e}")
            
            # 测试安全配置端点
            try:
                response = await client.get(f"{base_url}/security/config")
                if response.status_code == 200:
                    config = response.json()
                    logger.info(f"✅ 安全配置端点正常，安全级别: {config.get('security_level', 'unknown')}")
                else:
                    logger.warning(f"⚠️ 安全配置端点状态码: {response.status_code}")
            except Exception as e:
                logger.error(f"❌ 安全配置端点测试失败: {e}")
                
    except ImportError:
        logger.warning("⚠️ httpx未安装，跳过API端点测试")
        logger.info("如需完整测试，请安装: pip install httpx")

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，正在关闭服务器...")
    sys.exit(0)

async def main():
    """主函数"""
    setup_logging()
    
    logger.info("=" * 60)
    logger.info("🤖 AI运维代理启动器 v1.0")
    logger.info("=" * 60)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 启动API服务器
    process = start_api_server()
    if not process:
        return
    
    # 测试API端点
    await test_api_endpoints()
    
    logger.info("=" * 60)
    logger.info("🎉 启动完成！")
    logger.info("API服务地址: http://localhost:8000")
    logger.info("API文档地址: http://localhost:8000/docs")
    logger.info("按 Ctrl+C 停止服务器")
    logger.info("=" * 60)
    
    # 保持运行
    try:
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        logger.info("👋 再见！")

if __name__ == "__main__":
    asyncio.run(main()) 